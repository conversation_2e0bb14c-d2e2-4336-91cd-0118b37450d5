<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من وجود استعلام البحث
if (!isset($_GET['q']) || empty(trim($_GET['q']))) {
    echo json_encode(['success' => false, 'results' => []]);
    exit;
}

$query = sanitizeInput($_GET['q']);
$user = null;

// التحقق من حالة تسجيل الدخول
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

try {
    $results = [];
    
    // البحث في الدروس
    $lessonQuery = "
        SELECT l.id, l.title, l.description, s.name as subject_name, g.name as grade_name, s.grade_id
        FROM lessons l 
        JOIN subjects s ON l.subject_id = s.id 
        JOIN grades g ON s.grade_id = g.id 
        WHERE l.status = 'published' 
        AND (l.title LIKE :query OR l.description LIKE :query OR l.keywords LIKE :query)
    ";
    
    // إضافة قيود الوصول للطلاب
    if ($user && $user['role'] == 'student' && $user['grade_id']) {
        $lessonQuery .= " AND g.id = :grade_id";
    }
    
    $lessonQuery .= " ORDER BY l.views_count DESC LIMIT 10";
    
    $params = ['query' => "%$query%"];
    if ($user && $user['role'] == 'student' && $user['grade_id']) {
        $params['grade_id'] = $user['grade_id'];
    }
    
    $lessons = fetchAll($lessonQuery, $params);
    
    foreach ($lessons as $lesson) {
        // التحقق من صلاحية الوصول
        if ($user && !canAccessGrade($user['id'], $lesson['grade_id'])) {
            continue;
        }
        
        $results[] = [
            'id' => $lesson['id'],
            'title' => $lesson['title'],
            'description' => $lesson['description'] ? substr($lesson['description'], 0, 100) . '...' : '',
            'type' => 'درس',
            'category' => $lesson['grade_name'] . ' - ' . $lesson['subject_name'],
            'url' => 'lesson.php?id=' . $lesson['id']
        ];
    }
    
    // البحث في المواد الدراسية
    $subjectQuery = "
        SELECT s.id, s.name, s.description, g.name as grade_name, g.id as grade_id
        FROM subjects s 
        JOIN grades g ON s.grade_id = g.id 
        WHERE s.status = 'active' 
        AND (s.name LIKE :query OR s.description LIKE :query)
    ";
    
    // إضافة قيود الوصول للطلاب
    if ($user && $user['role'] == 'student' && $user['grade_id']) {
        $subjectQuery .= " AND g.id = :grade_id";
    }
    
    $subjectQuery .= " ORDER BY s.name ASC LIMIT 5";
    
    $subjects = fetchAll($subjectQuery, $params);
    
    foreach ($subjects as $subject) {
        // التحقق من صلاحية الوصول
        if ($user && !canAccessGrade($user['id'], $subject['grade_id'])) {
            continue;
        }
        
        $results[] = [
            'id' => $subject['id'],
            'title' => $subject['name'],
            'description' => $subject['description'] ? substr($subject['description'], 0, 100) . '...' : '',
            'type' => 'مادة دراسية',
            'category' => $subject['grade_name'],
            'url' => 'lessons.php?subject=' . $subject['id']
        ];
    }
    
    // البحث في الصفوف الدراسية
    if (!$user || $user['role'] != 'student') {
        $gradeQuery = "
            SELECT id, name, description 
            FROM grades 
            WHERE status = 'active' 
            AND (name LIKE :query OR description LIKE :query)
            ORDER BY order_num ASC 
            LIMIT 5
        ";
        
        $grades = fetchAll($gradeQuery, ['query' => "%$query%"]);
        
        foreach ($grades as $grade) {
            $results[] = [
                'id' => $grade['id'],
                'title' => $grade['name'],
                'description' => $grade['description'] ? substr($grade['description'], 0, 100) . '...' : '',
                'type' => 'صف دراسي',
                'category' => 'المراحل الدراسية',
                'url' => 'subjects.php?grade=' . $grade['id']
            ];
        }
    }
    
    // ترتيب النتائج حسب الصلة
    usort($results, function($a, $b) use ($query) {
        $aScore = 0;
        $bScore = 0;
        
        // نقاط إضافية للعنوان المطابق
        if (stripos($a['title'], $query) !== false) $aScore += 10;
        if (stripos($b['title'], $query) !== false) $bScore += 10;
        
        // نقاط إضافية للدروس
        if ($a['type'] == 'درس') $aScore += 5;
        if ($b['type'] == 'درس') $bScore += 5;
        
        return $bScore - $aScore;
    });
    
    echo json_encode([
        'success' => true,
        'results' => array_slice($results, 0, 15), // أقصى 15 نتيجة
        'total' => count($results),
        'query' => $query
    ]);
    
} catch (Exception $e) {
    error_log("Search error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'results' => [],
        'message' => 'حدث خطأ أثناء البحث'
    ]);
}
?>
