<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';
$step = 'email'; // email, code, reset

// معالجة إرسال البريد الإلكتروني
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['send_email'])) {
    $email = sanitizeInput($_POST['email']);
    
    if (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        $user = getUserByEmail($email);
        
        if ($user) {
            // إنشاء رمز إعادة تعيين
            $resetCode = generateToken(6, true); // رمز من 6 أرقام
            $resetExpires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // حفظ رمز الإعادة في قاعدة البيانات
            $result = updateRecord('users', [
                'reset_token' => $resetCode,
                'reset_expires' => $resetExpires
            ], 'id = :id', ['id' => $user['id']]);
            
            if ($result) {
                // في بيئة الإنتاج، يجب إرسال الرمز عبر البريد الإلكتروني
                // هنا سنعرض الرمز مباشرة للاختبار
                $_SESSION['reset_email'] = $email;
                $_SESSION['reset_code_display'] = $resetCode; // للاختبار فقط
                $success = 'تم إرسال رمز إعادة التعيين إلى بريدك الإلكتروني';
                $step = 'code';
            } else {
                $error = 'حدث خطأ أثناء إرسال رمز الإعادة';
            }
        } else {
            $error = 'البريد الإلكتروني غير مسجل في النظام';
        }
    }
}

// معالجة التحقق من الرمز
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['verify_code'])) {
    $code = sanitizeInput($_POST['code']);
    $email = $_SESSION['reset_email'] ?? '';
    
    if (empty($code)) {
        $error = 'يرجى إدخال رمز التحقق';
    } elseif (empty($email)) {
        $error = 'انتهت صلاحية الجلسة. يرجى البدء من جديد';
        $step = 'email';
    } else {
        $user = fetchOne("
            SELECT * FROM users 
            WHERE email = :email 
            AND reset_token = :code 
            AND reset_expires > NOW()
        ", ['email' => $email, 'code' => $code]);
        
        if ($user) {
            $_SESSION['reset_user_id'] = $user['id'];
            $step = 'reset';
            $success = 'تم التحقق من الرمز بنجاح. يمكنك الآن إعادة تعيين كلمة المرور';
        } else {
            $error = 'رمز التحقق غير صحيح أو منتهي الصلاحية';
        }
    }
}

// معالجة إعادة تعيين كلمة المرور
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['reset_password'])) {
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    $userId = $_SESSION['reset_user_id'] ?? null;
    
    if (empty($password) || empty($confirmPassword)) {
        $error = 'يرجى إدخال كلمة المرور وتأكيدها';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirmPassword) {
        $error = 'كلمة المرور وتأكيدها غير متطابقتين';
    } elseif (!$userId) {
        $error = 'انتهت صلاحية الجلسة. يرجى البدء من جديد';
        $step = 'email';
    } else {
        // تحديث كلمة المرور
        $hashedPassword = hashPassword($password);
        $result = updateRecord('users', [
            'password' => $hashedPassword,
            'reset_token' => null,
            'reset_expires' => null
        ], 'id = :id', ['id' => $userId]);
        
        if ($result) {
            // تنظيف الجلسة
            unset($_SESSION['reset_email']);
            unset($_SESSION['reset_user_id']);
            unset($_SESSION['reset_code_display']);
            
            $success = 'تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول';
            
            // إعادة التوجيه لصفحة تسجيل الدخول بعد 3 ثوان
            header("refresh:3;url=login.php");
        } else {
            $error = 'حدث خطأ أثناء تغيير كلمة المرور';
        }
    }
}

// تحديد الخطوة الحالية
if (isset($_SESSION['reset_user_id'])) {
    $step = 'reset';
} elseif (isset($_SESSION['reset_email'])) {
    $step = 'code';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسيت كلمة المرور - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .forgot-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .forgot-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            margin: 0 auto;
        }
        .forgot-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .forgot-body {
            padding: 2rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: 600;
            position: relative;
        }
        .step.active {
            background: var(--primary-color);
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -20px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .step.completed::after {
            background: #28a745;
        }
        .form-floating label {
            right: 1rem;
            left: auto;
        }
        .form-floating input {
            padding-right: 1rem;
            padding-left: 3rem;
        }
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 5;
        }
        .code-input {
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: 600;
        }
        .test-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="forgot-container d-flex align-items-center py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="forgot-card">
                        <div class="forgot-header">
                            <i class="fas fa-key fa-3x mb-3"></i>
                            <h3>إعادة تعيين كلمة المرور</h3>
                            <p class="mb-0">
                                <?php if ($step == 'email'): ?>
                                    أدخل بريدك الإلكتروني لإرسال رمز الإعادة
                                <?php elseif ($step == 'code'): ?>
                                    أدخل رمز التحقق المرسل لبريدك
                                <?php else: ?>
                                    أدخل كلمة المرور الجديدة
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <div class="forgot-body">
                            <!-- Step Indicator -->
                            <div class="step-indicator">
                                <div class="step <?php echo $step == 'email' ? 'active' : ($step != 'email' ? 'completed' : ''); ?>">1</div>
                                <div class="step <?php echo $step == 'code' ? 'active' : ($step == 'reset' ? 'completed' : ''); ?>">2</div>
                                <div class="step <?php echo $step == 'reset' ? 'active' : ''; ?>">3</div>
                            </div>
                            
                            <?php if ($error): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo htmlspecialchars($error); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo htmlspecialchars($success); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($step == 'email'): ?>
                                <!-- Step 1: Email Input -->
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3 position-relative">
                                        <div class="form-floating">
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   placeholder="البريد الإلكتروني" required>
                                            <label for="email">البريد الإلكتروني</label>
                                            <i class="fas fa-envelope input-icon"></i>
                                        </div>
                                        <div class="invalid-feedback">
                                            يرجى إدخال بريد إلكتروني صحيح
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid mb-3">
                                        <button type="submit" name="send_email" class="btn btn-primary btn-lg">
                                            <i class="fas fa-paper-plane me-2"></i>إرسال رمز الإعادة
                                        </button>
                                    </div>
                                </form>
                                
                            <?php elseif ($step == 'code'): ?>
                                <!-- Step 2: Code Verification -->
                                <?php if (isset($_SESSION['reset_code_display'])): ?>
                                    <div class="test-info">
                                        <strong><i class="fas fa-info-circle me-2"></i>للاختبار:</strong>
                                        رمز التحقق هو: <strong><?php echo $_SESSION['reset_code_display']; ?></strong>
                                    </div>
                                <?php endif; ?>
                                
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="code" class="form-label">رمز التحقق</label>
                                        <input type="text" class="form-control code-input" id="code" name="code" 
                                               placeholder="000000" maxlength="6" required>
                                        <div class="form-text">أدخل الرمز المكون من 6 أرقام</div>
                                        <div class="invalid-feedback">
                                            يرجى إدخال رمز التحقق
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid mb-3">
                                        <button type="submit" name="verify_code" class="btn btn-primary btn-lg">
                                            <i class="fas fa-check me-2"></i>تحقق من الرمز
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="text-center">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        <i class="fas fa-arrow-right me-1"></i>العودة لإدخال البريد الإلكتروني
                                    </a>
                                </div>
                                
                            <?php elseif ($step == 'reset'): ?>
                                <!-- Step 3: Password Reset -->
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3 position-relative">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   placeholder="كلمة المرور الجديدة" minlength="6" required>
                                            <label for="password">كلمة المرور الجديدة</label>
                                            <i class="fas fa-lock input-icon"></i>
                                        </div>
                                        <div class="invalid-feedback">
                                            كلمة المرور يجب أن تكون 6 أحرف على الأقل
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3 position-relative">
                                        <div class="form-floating">
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   placeholder="تأكيد كلمة المرور" minlength="6" required>
                                            <label for="confirm_password">تأكيد كلمة المرور</label>
                                            <i class="fas fa-lock input-icon"></i>
                                        </div>
                                        <div class="invalid-feedback">
                                            يرجى تأكيد كلمة المرور
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid mb-3">
                                        <button type="submit" name="reset_password" class="btn btn-success btn-lg">
                                            <i class="fas fa-save me-2"></i>حفظ كلمة المرور الجديدة
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2">تذكرت كلمة المرور؟</p>
                                <a href="login.php" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </a>
                            </div>
                            
                            <div class="mt-4 text-center">
                                <a href="index.php" class="text-muted text-decoration-none">
                                    <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // التحقق من تطابق كلمات المرور
        const confirmPassword = document.getElementById('confirm_password');
        if (confirmPassword) {
            confirmPassword.addEventListener('input', function() {
                const password = document.getElementById('password').value;
                const confirmPasswordValue = this.value;
                
                if (password !== confirmPasswordValue) {
                    this.setCustomValidity('كلمات المرور غير متطابقة');
                } else {
                    this.setCustomValidity('');
                }
            });
        }
        
        // تحسين إدخال رمز التحقق
        const codeInput = document.getElementById('code');
        if (codeInput) {
            codeInput.addEventListener('input', function() {
                // السماح بالأرقام فقط
                this.value = this.value.replace(/[^0-9]/g, '');
            });
            
            codeInput.focus();
        }
        
        // التركيز على الحقل الأول
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.querySelector('input[type="email"], input[type="text"], input[type="password"]');
            if (firstInput) {
                firstInput.focus();
            }
        });
    </script>
</body>
</html>
