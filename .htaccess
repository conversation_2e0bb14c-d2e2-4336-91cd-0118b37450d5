# منصة التعليم التفاعلي - إعدادات Apache
# Educational Platform - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول لمجلدات النظام
RedirectMatch 404 /\.git
RedirectMatch 404 /config/
RedirectMatch 404 /includes/
RedirectMatch 404 /install/

# حماية من الهجمات الشائعة
<IfModule mod_rewrite.c>
    # منع SQL Injection
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # منع محاولات الوصول للملفات المخفية
    RewriteCond %{REQUEST_URI} (^|/)\.
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # الفيديو والصوت
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType audio/mpeg "access plus 1 month"
    
    # المستندات
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/msword "access plus 1 month"
    ExpiresByType application/vnd.ms-excel "access plus 1 month"
    ExpiresByType application/vnd.ms-powerpoint "access plus 1 month"
</IfModule>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت من مصادر غير موثوقة
    Header set X-Content-Type-Options nosniff
    
    # تفعيل حماية XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # إعدادات أمان المحتوى
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; media-src 'self' https:; frame-src 'self' https://www.youtube.com https://player.vimeo.com;"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# تحسين رفع الملفات
<IfModule mod_php.c>
    php_value upload_max_filesize 50M
    php_value post_max_size 50M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 256M
</IfModule>

# إعادة توجيه الروابط الودية
<IfModule mod_rewrite.c>
    # إزالة index.php من الروابط
    RewriteCond %{THE_REQUEST} /index\.php[?\s] [NC]
    RewriteRule ^index\.php$ / [R=301,L]
    
    # إعادة توجيه www إلى non-www (اختياري)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # إعادة توجيه HTTP إلى HTTPS (اختياري)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات الترميز
AddDefaultCharset UTF-8

# أنواع الملفات المدعومة
AddType application/font-woff .woff
AddType application/font-woff2 .woff2
AddType application/vnd.ms-fontobject .eot
AddType font/truetype .ttf
AddType font/opentype .otf

# معالجة الأخطاء المخصصة
ErrorDocument 400 /error.php?code=400
ErrorDocument 401 /error.php?code=401
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /error.php?code=404
ErrorDocument 500 /error.php?code=500

# حماية ملفات التكوين
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

# حماية ملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<Directory "uploads/">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# تحسين الأداء - تجميع الطلبات
<IfModule mod_rewrite.c>
    # تجميع ملفات CSS
    RewriteRule ^assets/css/combined\.css$ /combine.php?type=css [L]
    
    # تجميع ملفات JavaScript
    RewriteRule ^assets/js/combined\.js$ /combine.php?type=js [L]
</IfModule>

# إعدادات خاصة بالتطوير (يجب إزالتها في الإنتاج)
# php_flag display_errors Off
# php_flag log_errors On
# php_value error_log /path/to/error.log
