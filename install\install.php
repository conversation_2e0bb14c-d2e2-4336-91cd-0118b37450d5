<?php
/**
 * ملف تثبيت الموقع التعليمي
 * Educational Platform Installation Script
 */

// منع الوصول المباشر إذا كان الموقع مثبت بالفعل
if (file_exists('../config/installed.lock')) {
    die('الموقع مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف config/installed.lock');
}

require_once '../config/database.php';

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // تنفيذ سكريبت قاعدة البيانات
        $sql = file_get_contents('database.sql');
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql);
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $pdo->exec($query);
            }
        }
        
        // إدراج البيانات الأولية
        insertInitialData();
        
        // إنشاء ملف التثبيت
        file_put_contents('../config/installed.lock', date('Y-m-d H:i:s'));
        
        $success = true;
        
    } catch (Exception $e) {
        $errors[] = 'خطأ في التثبيت: ' . $e->getMessage();
    }
}

/**
 * إدراج البيانات الأولية
 */
function insertInitialData() {
    global $pdo;
    
    // إدراج الصفوف الدراسية
    $grades = [
        ['name' => 'الأول ابتدائي', 'level' => 'primary', 'color' => '#007bff', 'icon' => 'fa-baby', 'order_num' => 1],
        ['name' => 'الثاني ابتدائي', 'level' => 'primary', 'color' => '#28a745', 'icon' => 'fa-child', 'order_num' => 2],
        ['name' => 'الثالث ابتدائي', 'level' => 'primary', 'color' => '#17a2b8', 'icon' => 'fa-user-graduate', 'order_num' => 3],
        ['name' => 'الرابع ابتدائي', 'level' => 'primary', 'color' => '#ffc107', 'icon' => 'fa-book', 'order_num' => 4],
        ['name' => 'الخامس ابتدائي', 'level' => 'primary', 'color' => '#dc3545', 'icon' => 'fa-pencil-alt', 'order_num' => 5],
        ['name' => 'السادس ابتدائي', 'level' => 'primary', 'color' => '#6f42c1', 'icon' => 'fa-graduation-cap', 'order_num' => 6],
        ['name' => 'الأول متوسط', 'level' => 'middle', 'color' => '#fd7e14', 'icon' => 'fa-book-open', 'order_num' => 7],
        ['name' => 'الثاني متوسط', 'level' => 'middle', 'color' => '#20c997', 'icon' => 'fa-chalkboard', 'order_num' => 8],
        ['name' => 'الثالث متوسط', 'level' => 'middle', 'color' => '#e83e8c', 'icon' => 'fa-user-graduate', 'order_num' => 9],
        ['name' => 'الأول ثانوي', 'level' => 'secondary', 'color' => '#6610f2', 'icon' => 'fa-university', 'order_num' => 10],
        ['name' => 'الثاني ثانوي', 'level' => 'secondary', 'color' => '#6f42c1', 'icon' => 'fa-graduation-cap', 'order_num' => 11],
        ['name' => 'الثالث ثانوي', 'level' => 'secondary', 'color' => '#495057', 'icon' => 'fa-medal', 'order_num' => 12]
    ];
    
    foreach ($grades as $grade) {
        insertRecord('grades', $grade);
    }
    
    // إدراج المواد الدراسية للصف الأول ابتدائي كمثال
    $subjects = [
        ['name' => 'اللغة العربية', 'grade_id' => 1, 'color' => '#28a745', 'icon' => 'fa-language', 'order_num' => 1],
        ['name' => 'الرياضيات', 'grade_id' => 1, 'color' => '#007bff', 'icon' => 'fa-calculator', 'order_num' => 2],
        ['name' => 'العلوم', 'grade_id' => 1, 'color' => '#17a2b8', 'icon' => 'fa-flask', 'order_num' => 3],
        ['name' => 'التربية الإسلامية', 'grade_id' => 1, 'color' => '#6f42c1', 'icon' => 'fa-mosque', 'order_num' => 4],
        ['name' => 'التربية الفنية', 'grade_id' => 1, 'color' => '#e83e8c', 'icon' => 'fa-palette', 'order_num' => 5],
        ['name' => 'التربية البدنية', 'grade_id' => 1, 'color' => '#fd7e14', 'icon' => 'fa-running', 'order_num' => 6]
    ];
    
    foreach ($subjects as $subject) {
        insertRecord('subjects', $subject);
    }
    
    // إنشاء حساب المدير الافتراضي
    $adminData = [
        'name' => 'المدير العام',
        'email' => '<EMAIL>',
        'password' => password_hash('admin123', PASSWORD_DEFAULT),
        'role' => 'admin',
        'status' => 'active',
        'email_verified' => 1
    ];
    
    insertRecord('users', $adminData);
    
    // إدراج بعض الدروس التجريبية
    $lessons = [
        [
            'title' => 'مقدمة في الحروف العربية',
            'description' => 'تعلم الحروف العربية الأساسية',
            'content' => '<h3>الحروف العربية</h3><p>في هذا الدرس سنتعلم الحروف العربية من الألف إلى الياء...</p>',
            'subject_id' => 1,
            'teacher_id' => 1,
            'order_num' => 1,
            'difficulty' => 'easy',
            'estimated_time' => 30,
            'objectives' => 'تعلم الحروف العربية الأساسية وطريقة كتابتها',
            'keywords' => 'حروف، عربية، كتابة، قراءة',
            'status' => 'published'
        ],
        [
            'title' => 'الأرقام من 1 إلى 10',
            'description' => 'تعلم الأرقام الأساسية',
            'content' => '<h3>الأرقام</h3><p>سنتعلم في هذا الدرس الأرقام من 1 إلى 10...</p>',
            'subject_id' => 2,
            'teacher_id' => 1,
            'order_num' => 1,
            'difficulty' => 'easy',
            'estimated_time' => 25,
            'objectives' => 'تعلم الأرقام من 1 إلى 10 وطريقة كتابتها',
            'keywords' => 'أرقام، رياضيات، عد، حساب',
            'status' => 'published'
        ]
    ];
    
    foreach ($lessons as $lesson) {
        insertRecord('lessons', $lesson);
    }
    
    // إدراج الإعدادات الافتراضية
    $settings = [
        ['setting_key' => 'site_name', 'setting_value' => 'منصة التعليم التفاعلي', 'setting_type' => 'text'],
        ['setting_key' => 'site_description', 'setting_value' => 'منصة تعليمية شاملة لجميع المراحل الدراسية', 'setting_type' => 'text'],
        ['setting_key' => 'admin_email', 'setting_value' => '<EMAIL>', 'setting_type' => 'text'],
        ['setting_key' => 'registration_enabled', 'setting_value' => '1', 'setting_type' => 'boolean'],
        ['setting_key' => 'email_verification_required', 'setting_value' => '0', 'setting_type' => 'boolean'],
        ['setting_key' => 'max_file_size', 'setting_value' => '10485760', 'setting_type' => 'number'],
        ['setting_key' => 'allowed_file_types', 'setting_value' => 'pdf,doc,docx,ppt,pptx,jpg,jpeg,png,mp4,mp3', 'setting_type' => 'text']
    ];
    
    foreach ($settings as $setting) {
        insertRecord('settings', $setting);
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت منصة التعليم التفاعلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-8 col-lg-6">
                <div class="install-card">
                    <div class="install-header">
                        <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                        <h2>تثبيت منصة التعليم التفاعلي</h2>
                        <p class="mb-0">مرحباً بك في معالج التثبيت</p>
                    </div>
                    
                    <div class="install-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>تم التثبيت بنجاح!</strong>
                                <p class="mb-2 mt-2">تم إنشاء قاعدة البيانات وإدراج البيانات الأولية بنجاح.</p>
                                <hr>
                                <h6>بيانات تسجيل الدخول للمدير:</h6>
                                <ul class="mb-0">
                                    <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
                                    <li><strong>كلمة المرور:</strong> admin123</li>
                                </ul>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="../index.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>الذهاب للموقع
                                </a>
                                <a href="../login.php" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول كمدير
                                </a>
                            </div>
                            
                        <?php elseif (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>حدثت أخطاء أثناء التثبيت:</strong>
                                <ul class="mb-0 mt-2">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-warning" onclick="location.reload()">
                                    <i class="fas fa-redo me-2"></i>إعادة المحاولة
                                </button>
                            </div>
                            
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>قبل البدء في التثبيت:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>تأكد من إعداد قاعدة البيانات في ملف config/database.php</li>
                                    <li>تأكد من صلاحيات الكتابة في مجلدات uploads و cache</li>
                                    <li>تأكد من تفعيل PHP extensions المطلوبة (PDO, GD, etc.)</li>
                                </ul>
                            </div>
                            
                            <form method="POST">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-download me-2"></i>بدء التثبيت
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
