/**
 * ملف JavaScript الأساسي للموقع التعليمي
 * Main JavaScript File for Educational Platform
 */

// إعدادات عامة
const CONFIG = {
    apiUrl: 'api/',
    loadingText: 'جاري التحميل...',
    errorText: 'حدث خطأ، يرجى المحاولة مرة أخرى',
    successText: 'تم بنجاح'
};

// دالة التهيئة الأساسية
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الأزرار التفاعلية
    initializeInteractiveButtons();
    
    // تهيئة التحميل التدريجي
    initializeLazyLoading();
    
    // تهيئة البحث
    initializeSearch();
    
    console.log('تم تهيئة التطبيق بنجاح');
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // تحسين حقول الإدخال
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
}

/**
 * تهيئة الأزرار التفاعلية
 */
function initializeInteractiveButtons() {
    // أزرار الإعجاب والمفضلة
    const favoriteButtons = document.querySelectorAll('.btn-favorite');
    favoriteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            toggleFavorite(this);
        });
    });
    
    // أزرار المشاركة
    const shareButtons = document.querySelectorAll('.btn-share');
    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            shareContent(this);
        });
    });
}

/**
 * تبديل حالة المفضلة
 */
function toggleFavorite(button) {
    const lessonId = button.dataset.lessonId;
    const icon = button.querySelector('i');
    const isActive = button.classList.contains('active');
    
    // تغيير الحالة المرئية فوراً
    if (isActive) {
        button.classList.remove('active');
        icon.classList.remove('fas');
        icon.classList.add('far');
    } else {
        button.classList.add('active');
        icon.classList.remove('far');
        icon.classList.add('fas');
    }
    
    // إرسال طلب AJAX
    fetch('api/toggle-favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            lesson_id: lessonId,
            action: isActive ? 'remove' : 'add'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            // إعادة الحالة السابقة في حالة الخطأ
            if (isActive) {
                button.classList.add('active');
                icon.classList.add('fas');
                icon.classList.remove('far');
            } else {
                button.classList.remove('active');
                icon.classList.add('far');
                icon.classList.remove('fas');
            }
            showAlert('error', data.message || CONFIG.errorText);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', CONFIG.errorText);
    });
}

/**
 * مشاركة المحتوى
 */
function shareContent(button) {
    const url = button.dataset.url || window.location.href;
    const title = button.dataset.title || document.title;
    
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(url).then(() => {
            showAlert('success', 'تم نسخ الرابط');
        }).catch(() => {
            showAlert('error', 'فشل في نسخ الرابط');
        });
    }
}

/**
 * تهيئة التحميل التدريجي للصور
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    } else {
        // بديل للمتصفحات القديمة
        images.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
        });
    }
}

/**
 * تهيئة البحث
 */
function initializeSearch() {
    const searchInput = document.querySelector('#searchInput');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            } else {
                clearSearchResults();
            }
        });
    }
}

/**
 * تنفيذ البحث
 */
function performSearch(query) {
    const resultsContainer = document.querySelector('#searchResults');
    if (!resultsContainer) return;
    
    // عرض مؤشر التحميل
    resultsContainer.innerHTML = '<div class="text-center p-3"><div class="loading-spinner"></div></div>';
    
    fetch(`api/search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results);
        })
        .catch(error => {
            console.error('Search error:', error);
            resultsContainer.innerHTML = '<div class="text-center p-3 text-muted">حدث خطأ في البحث</div>';
        });
}

/**
 * عرض نتائج البحث
 */
function displaySearchResults(results) {
    const resultsContainer = document.querySelector('#searchResults');
    if (!resultsContainer) return;
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="text-center p-3 text-muted">لا توجد نتائج</div>';
        return;
    }
    
    let html = '<div class="list-group">';
    results.forEach(result => {
        html += `
            <a href="${result.url}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${result.title}</h6>
                    <small class="text-muted">${result.type}</small>
                </div>
                <p class="mb-1">${result.description}</p>
            </a>
        `;
    });
    html += '</div>';
    
    resultsContainer.innerHTML = html;
}

/**
 * مسح نتائج البحث
 */
function clearSearchResults() {
    const resultsContainer = document.querySelector('#searchResults');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
}

/**
 * عرض التنبيهات
 */
function showAlert(type, message, duration = 5000) {
    const alertContainer = document.querySelector('#alertContainer') || createAlertContainer();
    
    const alertId = 'alert-' + Date.now();
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    
    const alertHtml = `
        <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        const alert = document.querySelector(`#${alertId}`);
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, duration);
}

/**
 * إنشاء حاوية التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alertContainer';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

/**
 * تحديث شريط التقدم
 */
function updateProgressBar(elementId, progress) {
    const progressBar = document.querySelector(`#${elementId}`);
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
        progressBar.setAttribute('aria-valuenow', progress);
        progressBar.textContent = `${Math.round(progress)}%`;
    }
}

/**
 * تحميل المحتوى بـ AJAX
 */
function loadContent(url, containerId, showLoading = true) {
    const container = document.querySelector(`#${containerId}`);
    if (!container) return;
    
    if (showLoading) {
        container.innerHTML = '<div class="text-center p-4"><div class="loading-spinner"></div><p class="mt-2">جاري التحميل...</p></div>';
    }
    
    fetch(url)
        .then(response => response.text())
        .then(html => {
            container.innerHTML = html;
            // إعادة تهيئة العناصر التفاعلية الجديدة
            initializeInteractiveButtons();
        })
        .catch(error => {
            console.error('Load content error:', error);
            container.innerHTML = '<div class="text-center p-4 text-danger">حدث خطأ في تحميل المحتوى</div>';
        });
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

/**
 * تنسيق الأرقام العربية
 */
function formatArabicNumber(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, (digit) => arabicNumbers[digit]);
}

/**
 * تحسين الأداء - تأخير تنفيذ الدوال
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * التحقق من الاتصال بالإنترنت
 */
function checkConnection() {
    if (!navigator.onLine) {
        showAlert('warning', 'لا يوجد اتصال بالإنترنت');
        return false;
    }
    return true;
}

// مراقبة حالة الاتصال
window.addEventListener('online', () => {
    showAlert('success', 'تم استعادة الاتصال بالإنترنت');
});

window.addEventListener('offline', () => {
    showAlert('warning', 'انقطع الاتصال بالإنترنت');
});

// تصدير الدوال للاستخدام العام
window.EducationalPlatform = {
    showAlert,
    loadContent,
    updateProgressBar,
    confirmDelete,
    formatArabicNumber,
    debounce,
    checkConnection
};
