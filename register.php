<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';

// جلب الصفوف الدراسية
$grades = getAllGrades();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = sanitizeInput($_POST['role']);
    $grade_id = isset($_POST['grade_id']) ? (int)$_POST['grade_id'] : null;
    $phone = sanitizeInput($_POST['phone']);
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($password) || empty($role)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (!in_array($role, ['student', 'teacher', 'parent'])) {
        $error = 'نوع المستخدم غير صحيح';
    } elseif ($role == 'student' && !$grade_id) {
        $error = 'يرجى اختيار الصف الدراسي للطالب';
    } else {
        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        if (getUserByEmail($email)) {
            $error = 'البريد الإلكتروني مستخدم بالفعل';
        } else {
            // إنشاء المستخدم
            $userData = [
                'name' => $name,
                'email' => $email,
                'password' => $password,
                'role' => $role,
                'grade_id' => $role == 'student' ? $grade_id : null,
                'phone' => $phone,
                'status' => 'active',
                'email_verified' => 0
            ];
            
            $userId = createUser($userData);
            
            if ($userId) {
                $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                
                // تسجيل الدخول التلقائي
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_name'] = $name;
                $_SESSION['user_role'] = $role;
                $_SESSION['user_grade'] = $grade_id;
                
                // إعادة التوجيه بعد ثانيتين
                header("refresh:2;url=index.php");
            } else {
                $error = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .register-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .form-floating label {
            right: 1rem;
            left: auto;
        }
        .form-floating input,
        .form-floating select {
            padding-right: 1rem;
        }
        .grade-selection {
            display: none;
        }
        .grade-selection.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="register-card">
                        <div class="register-header">
                            <i class="fas fa-user-plus fa-3x mb-3"></i>
                            <h3>إنشاء حساب جديد</h3>
                            <p class="mb-0">انضم إلى منصة التعليم التفاعلي</p>
                        </div>
                        
                        <div class="register-body">
                            <?php if ($error): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo htmlspecialchars($error); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo htmlspecialchars($success); ?>
                                    <div class="mt-2">
                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                        جاري إعادة التوجيه...
                                    </div>
                                </div>
                            <?php else: ?>
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-floating">
                                                    <input type="text" class="form-control" id="name" name="name" 
                                                           placeholder="الاسم الكامل" required 
                                                           value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>">
                                                    <label for="name">الاسم الكامل</label>
                                                </div>
                                                <div class="invalid-feedback">
                                                    يرجى إدخال الاسم الكامل
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-floating">
                                                    <input type="email" class="form-control" id="email" name="email" 
                                                           placeholder="البريد الإلكتروني" required 
                                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                                    <label for="email">البريد الإلكتروني</label>
                                                </div>
                                                <div class="invalid-feedback">
                                                    يرجى إدخال بريد إلكتروني صحيح
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-floating">
                                                    <input type="password" class="form-control" id="password" name="password" 
                                                           placeholder="كلمة المرور" required minlength="6">
                                                    <label for="password">كلمة المرور</label>
                                                </div>
                                                <div class="invalid-feedback">
                                                    كلمة المرور يجب أن تكون 6 أحرف على الأقل
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-floating">
                                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                           placeholder="تأكيد كلمة المرور" required>
                                                    <label for="confirm_password">تأكيد كلمة المرور</label>
                                                </div>
                                                <div class="invalid-feedback">
                                                    يرجى تأكيد كلمة المرور
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-floating">
                                                    <select class="form-select" id="role" name="role" required>
                                                        <option value="">اختر نوع المستخدم</option>
                                                        <option value="student" <?php echo (isset($_POST['role']) && $_POST['role'] == 'student') ? 'selected' : ''; ?>>طالب</option>
                                                        <option value="teacher" <?php echo (isset($_POST['role']) && $_POST['role'] == 'teacher') ? 'selected' : ''; ?>>معلم</option>
                                                        <option value="parent" <?php echo (isset($_POST['role']) && $_POST['role'] == 'parent') ? 'selected' : ''; ?>>ولي أمر</option>
                                                    </select>
                                                    <label for="role">نوع المستخدم</label>
                                                </div>
                                                <div class="invalid-feedback">
                                                    يرجى اختيار نوع المستخدم
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-floating">
                                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                                           placeholder="رقم الهاتف (اختياري)" 
                                                           value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                                    <label for="phone">رقم الهاتف (اختياري)</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="grade-selection" id="gradeSelection">
                                        <div class="mb-3">
                                            <div class="form-floating">
                                                <select class="form-select" id="grade_id" name="grade_id">
                                                    <option value="">اختر الصف الدراسي</option>
                                                    <?php foreach ($grades as $grade): ?>
                                                        <option value="<?php echo $grade['id']; ?>" 
                                                                <?php echo (isset($_POST['grade_id']) && $_POST['grade_id'] == $grade['id']) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($grade['name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <label for="grade_id">الصف الدراسي</label>
                                            </div>
                                            <div class="invalid-feedback">
                                                يرجى اختيار الصف الدراسي
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="terms" required>
                                            <label class="form-check-label" for="terms">
                                                أوافق على <a href="#" class="text-decoration-none">شروط الاستخدام</a> و <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                                            </label>
                                            <div class="invalid-feedback">
                                                يجب الموافقة على الشروط والأحكام
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid mb-3">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2">لديك حساب بالفعل؟</p>
                                <a href="login.php" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </a>
                            </div>
                            
                            <div class="mt-4 text-center">
                                <a href="index.php" class="text-muted text-decoration-none">
                                    <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // إظهار/إخفاء اختيار الصف حسب نوع المستخدم
        document.getElementById('role').addEventListener('change', function() {
            const gradeSelection = document.getElementById('gradeSelection');
            const gradeSelect = document.getElementById('grade_id');
            
            if (this.value === 'student') {
                gradeSelection.classList.add('show');
                gradeSelect.required = true;
            } else {
                gradeSelection.classList.remove('show');
                gradeSelect.required = false;
                gradeSelect.value = '';
            }
        });
        
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // التركيز على حقل الاسم
            document.getElementById('name').focus();
            
            // إظهار اختيار الصف إذا كان الطالب محدد مسبقاً
            const roleSelect = document.getElementById('role');
            if (roleSelect.value === 'student') {
                document.getElementById('gradeSelection').classList.add('show');
                document.getElementById('grade_id').required = true;
            }
        });
    </script>
</body>
</html>
