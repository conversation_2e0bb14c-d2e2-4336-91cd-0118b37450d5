<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
    if ($user) {
        switch ($user['role']) {
            case 'admin':
                header('Location: admin/dashboard.php');
                break;
            case 'teacher':
                header('Location: teacher/dashboard.php');
                break;
            case 'parent':
                header('Location: parent/dashboard.php');
                break;
            default:
                header('Location: index.php');
        }
        exit;
    }
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        $user = getUserByEmail($email);
        
        if ($user && verifyPassword($password, $user['password'])) {
            if ($user['status'] != 'active') {
                $error = 'حسابك غير مفعل. يرجى التواصل مع الإدارة';
            } else {
                // تسجيل الدخول بنجاح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['user_grade'] = $user['grade_id'];
                
                // تحديث آخر تسجيل دخول
                updateRecord('users', 
                    ['last_login' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $user['id']]
                );
                
                // تذكر المستخدم
                if ($remember) {
                    $token = generateToken();
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                    updateRecord('users', 
                        ['remember_token' => $token], 
                        'id = :id', 
                        ['id' => $user['id']]
                    );
                }
                
                // إعادة التوجيه حسب نوع المستخدم
                switch ($user['role']) {
                    case 'admin':
                        header('Location: admin/dashboard.php');
                        break;
                    case 'teacher':
                        header('Location: teacher/dashboard.php');
                        break;
                    case 'parent':
                        header('Location: parent/dashboard.php');
                        break;
                    default:
                        header('Location: index.php');
                }
                exit;
            }
        } else {
            $error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-floating label {
            right: 1rem;
            left: auto;
        }
        .form-floating input {
            padding-right: 1rem;
            padding-left: 3rem;
        }
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 5;
        }
    </style>
</head>
<body>
    <div class="login-container d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card">
                        <div class="login-header">
                            <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                            <h3>تسجيل الدخول</h3>
                            <p class="mb-0">مرحباً بك في منصة التعليم التفاعلي</p>
                        </div>
                        
                        <div class="login-body">
                            <?php if ($error): ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo htmlspecialchars($error); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo htmlspecialchars($success); ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" class="needs-validation" novalidate>
                                <div class="mb-3 position-relative">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="email" name="email" 
                                               placeholder="البريد الإلكتروني" required 
                                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                        <label for="email">البريد الإلكتروني</label>
                                        <i class="fas fa-envelope input-icon"></i>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال بريد إلكتروني صحيح
                                    </div>
                                </div>
                                
                                <div class="mb-3 position-relative">
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="كلمة المرور" required>
                                        <label for="password">كلمة المرور</label>
                                        <i class="fas fa-lock input-icon"></i>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            تذكرني
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                    </button>
                                </div>
                                
                                <div class="text-center">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        نسيت كلمة المرور؟
                                    </a>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2">ليس لديك حساب؟</p>
                                <a href="register.php" class="btn btn-outline-primary">
                                    <i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد
                                </a>
                            </div>
                            
                            <div class="mt-4 text-center">
                                <a href="index.php" class="text-muted text-decoration-none">
                                    <i class="fas fa-arrow-right me-1"></i>العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات تجريبية للتسجيل -->
                    <div class="mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle me-2"></i>حسابات تجريبية
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>المدير:</strong><br>
                                        <small><EMAIL></small><br>
                                        <small>admin123</small>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>معلم:</strong><br>
                                        <small><EMAIL></small><br>
                                        <small>teacher123</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // التركيز على حقل البريد الإلكتروني
            document.getElementById('email').focus();
            
            // إظهار/إخفاء كلمة المرور
            const passwordField = document.getElementById('password');
            const toggleButton = document.createElement('button');
            toggleButton.type = 'button';
            toggleButton.className = 'btn btn-outline-secondary position-absolute';
            toggleButton.style.cssText = 'left: 0.5rem; top: 50%; transform: translateY(-50%); z-index: 10; border: none; background: none;';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
            
            passwordField.parentElement.style.position = 'relative';
            passwordField.parentElement.appendChild(toggleButton);
            
            toggleButton.addEventListener('click', function() {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                this.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
            });
        });
    </script>
</body>
</html>
