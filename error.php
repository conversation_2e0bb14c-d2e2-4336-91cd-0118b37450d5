<?php
// صفحة معالجة الأخطاء
$errorCode = isset($_GET['code']) ? (int)$_GET['code'] : 404;

// تحديد رسائل الأخطاء
$errorMessages = [
    400 => [
        'title' => 'طلب غير صحيح',
        'message' => 'الطلب الذي أرسلته غير صحيح أو غير مكتمل.',
        'icon' => 'fa-exclamation-triangle'
    ],
    401 => [
        'title' => 'غير مصرح',
        'message' => 'يجب تسجيل الدخول للوصول لهذه الصفحة.',
        'icon' => 'fa-lock'
    ],
    403 => [
        'title' => 'ممنوع الوصول',
        'message' => 'ليس لديك صلاحية للوصول لهذه الصفحة.',
        'icon' => 'fa-ban'
    ],
    404 => [
        'title' => 'الصفحة غير موجودة',
        'message' => 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها.',
        'icon' => 'fa-search'
    ],
    500 => [
        'title' => 'خطأ في الخادم',
        'message' => 'حدث خطأ داخلي في الخادم. يرجى المحاولة لاحقاً.',
        'icon' => 'fa-server'
    ]
];

$error = $errorMessages[$errorCode] ?? $errorMessages[404];

// تعيين رمز الاستجابة HTTP
http_response_code($errorCode);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error['title']; ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-icon {
            font-size: 5rem;
            color: #6c757d;
            margin-bottom: 2rem;
            opacity: 0.7;
        }
        .error-code {
            font-size: 4rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #343a40;
            margin-bottom: 1rem;
        }
        .error-message {
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        .btn-back {
            background: transparent;
            border: 2px solid #6c757d;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            color: #6c757d;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-left: 1rem;
        }
        .btn-back:hover {
            background: #6c757d;
            color: white;
        }
        .suggestions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: right;
        }
        .suggestions h6 {
            color: #495057;
            margin-bottom: 1rem;
        }
        .suggestions ul {
            margin: 0;
            padding-right: 1rem;
        }
        .suggestions li {
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="error-container">
        <div class="error-icon">
            <i class="fas <?php echo $error['icon']; ?>"></i>
        </div>
        
        <div class="error-code"><?php echo $errorCode; ?></div>
        
        <h1 class="error-title"><?php echo $error['title']; ?></h1>
        
        <p class="error-message"><?php echo $error['message']; ?></p>
        
        <div class="d-flex justify-content-center flex-wrap gap-2">
            <a href="index.php" class="btn-home">
                <i class="fas fa-home me-2"></i>العودة للرئيسية
            </a>
            <a href="javascript:history.back()" class="btn-back">
                <i class="fas fa-arrow-right me-2"></i>الصفحة السابقة
            </a>
        </div>
        
        <?php if ($errorCode == 404): ?>
            <div class="suggestions">
                <h6><i class="fas fa-lightbulb me-2"></i>اقتراحات مفيدة:</h6>
                <ul>
                    <li>تأكد من كتابة الرابط بشكل صحيح</li>
                    <li>استخدم شريط البحث للعثور على ما تبحث عنه</li>
                    <li>تصفح الصفوف الدراسية المتاحة</li>
                    <li>تواصل معنا إذا كنت تعتقد أن هذا خطأ</li>
                </ul>
            </div>
        <?php elseif ($errorCode == 401): ?>
            <div class="suggestions">
                <h6><i class="fas fa-info-circle me-2"></i>للمتابعة:</h6>
                <ul>
                    <li><a href="login.php" class="text-decoration-none">سجل دخولك</a> إذا كان لديك حساب</li>
                    <li><a href="register.php" class="text-decoration-none">أنشئ حساب جديد</a> إذا لم يكن لديك حساب</li>
                    <li>تواصل مع الإدارة إذا كنت تواجه مشاكل في تسجيل الدخول</li>
                </ul>
            </div>
        <?php elseif ($errorCode == 500): ?>
            <div class="suggestions">
                <h6><i class="fas fa-tools me-2"></i>ما يمكنك فعله:</h6>
                <ul>
                    <li>انتظر قليلاً ثم حاول مرة أخرى</li>
                    <li>تأكد من اتصالك بالإنترنت</li>
                    <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                    <li>تواصل مع الدعم الفني إذا استمرت المشكلة</li>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <small class="text-muted">
                إذا كنت تعتقد أن هذا خطأ، يرجى 
                <a href="mailto:<EMAIL>" class="text-decoration-none">التواصل معنا</a>
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تأثيرات بصرية إضافية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير ظهور تدريجي
            const container = document.querySelector('.error-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            container.style.transition = 'all 0.6s ease';
            
            setTimeout(() => {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
            
            // تسجيل الخطأ للتحليل (اختياري)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'page_error', {
                    'error_code': <?php echo $errorCode; ?>,
                    'page_location': window.location.href
                });
            }
        });
    </script>
</body>
</html>
