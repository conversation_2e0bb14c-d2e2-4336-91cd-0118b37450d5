<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    header('Location: login.php');
    exit;
}

$error = '';
$success = '';

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    
    if (empty($name) || empty($email)) {
        $error = 'الاسم والبريد الإلكتروني مطلوبان';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
        $existingUser = fetchOne("SELECT id FROM users WHERE email = :email AND id != :id", [
            'email' => $email,
            'id' => $user['id']
        ]);
        
        if ($existingUser) {
            $error = 'البريد الإلكتروني مستخدم بالفعل';
        } else {
            $updateData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone
            ];
            
            $result = updateRecord('users', $updateData, 'id = :id', ['id' => $user['id']]);
            
            if ($result) {
                $success = 'تم تحديث الملف الشخصي بنجاح';
                $_SESSION['user_name'] = $name;
                $user = getUserById($user['id']); // إعادة جلب البيانات المحدثة
            } else {
                $error = 'حدث خطأ أثناء تحديث الملف الشخصي';
            }
        }
    }
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $error = 'جميع حقول كلمة المرور مطلوبة';
    } elseif (!verifyPassword($currentPassword, $user['password'])) {
        $error = 'كلمة المرور الحالية غير صحيحة';
    } elseif (strlen($newPassword) < 6) {
        $error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
    } elseif ($newPassword !== $confirmPassword) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
    } else {
        $hashedPassword = hashPassword($newPassword);
        $result = updateRecord('users', ['password' => $hashedPassword], 'id = :id', ['id' => $user['id']]);
        
        if ($result) {
            $success = 'تم تغيير كلمة المرور بنجاح';
        } else {
            $error = 'حدث خطأ أثناء تغيير كلمة المرور';
        }
    }
}

// جلب إحصائيات المستخدم
$stats = getUserStats($user['id']);

// جلب الصفوف الدراسية للطلاب
$grades = [];
if ($user['role'] == 'student') {
    $grades = getAllGrades();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: 700;
            margin: 0 auto 1.5rem;
            border: 4px solid rgba(255,255,255,0.2);
        }
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid var(--card-color, #007bff);
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--card-color, #007bff);
            margin-bottom: 0.5rem;
        }
        .stats-label {
            color: #6c757d;
            font-weight: 600;
            font-size: 0.9rem;
        }
        .role-badge {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        .role-student { background: #e3f2fd; color: #1976d2; }
        .role-teacher { background: #e8f5e8; color: #388e3c; }
        .role-parent { background: #fff3e0; color: #f57c00; }
        .role-admin { background: #fce4ec; color: #c2185b; }
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .section-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my-courses.php">
                            <i class="fas fa-book-open me-1"></i>دروسي
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($user['name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                            </a></li>
                            <?php if ($user['role'] == 'admin'): ?>
                                <li><a class="dropdown-item" href="admin/dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a></li>
                            <?php elseif ($user['role'] == 'parent'): ?>
                                <li><a class="dropdown-item" href="parent/dashboard.php">
                                    <i class="fas fa-child me-2"></i>متابعة الأبناء
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-3">الملف الشخصي</h1>
                    <p class="lead mb-0">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Profile Overview -->
                <div class="col-lg-4">
                    <div class="profile-card text-center">
                        <div class="profile-avatar">
                            <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
                        </div>
                        
                        <h3 class="mb-2"><?php echo htmlspecialchars($user['name']); ?></h3>
                        <p class="text-muted mb-3"><?php echo htmlspecialchars($user['email']); ?></p>
                        
                        <span class="role-badge role-<?php echo $user['role']; ?>">
                            <?php 
                            $roles = ['student' => 'طالب', 'teacher' => 'معلم', 'parent' => 'ولي أمر', 'admin' => 'مدير'];
                            echo $roles[$user['role']];
                            ?>
                        </span>
                        
                        <hr class="my-4">
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <strong class="d-block"><?php echo formatArabicDate($user['created_at']); ?></strong>
                                <small class="text-muted">تاريخ التسجيل</small>
                            </div>
                            <div class="col-6">
                                <strong class="d-block"><?php echo $user['last_login'] ? formatArabicDate($user['last_login']) : 'لم يسجل دخول'; ?></strong>
                                <small class="text-muted">آخر تسجيل دخول</small>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics for Students -->
                    <?php if ($user['role'] == 'student'): ?>
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="stats-card" style="--card-color: #28a745">
                                    <div class="stats-number"><?php echo $stats['completed_lessons']; ?></div>
                                    <div class="stats-label">دروس مكتملة</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stats-card" style="--card-color: #ffc107">
                                    <div class="stats-number"><?php echo $stats['in_progress_lessons']; ?></div>
                                    <div class="stats-label">قيد التقدم</div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="stats-card" style="--card-color: #17a2b8">
                                    <div class="stats-number"><?php echo round($stats['avg_progress']); ?>%</div>
                                    <div class="stats-label">متوسط التقدم</div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Profile Forms -->
                <div class="col-lg-8">
                    <!-- Personal Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            المعلومات الشخصية
                        </h4>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo htmlspecialchars($user['name']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($user['phone']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="role" class="form-label">نوع المستخدم</label>
                                        <input type="text" class="form-control" id="role" 
                                               value="<?php echo $roles[$user['role']]; ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($user['role'] == 'student' && $user['grade_id']): ?>
                                <div class="mb-3">
                                    <label for="grade" class="form-label">الصف الدراسي</label>
                                    <?php 
                                    $currentGrade = fetchOne("SELECT name FROM grades WHERE id = :id", ['id' => $user['grade_id']]);
                                    ?>
                                    <input type="text" class="form-control" id="grade" 
                                           value="<?php echo htmlspecialchars($currentGrade['name']); ?>" readonly>
                                </div>
                            <?php endif; ?>
                            
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </form>
                    </div>

                    <!-- Change Password -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            تغيير كلمة المرور
                        </h4>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" 
                                               minlength="6" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                               minlength="6" required>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" name="change_password" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </button>
                        </form>
                    </div>

                    <!-- Account Settings -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            إعدادات الحساب
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded mb-3">
                                    <div>
                                        <strong>حالة الحساب</strong>
                                        <br><small class="text-muted">حالة تفعيل حسابك</small>
                                    </div>
                                    <span class="badge bg-<?php echo $user['status'] == 'active' ? 'success' : 'danger'; ?>">
                                        <?php echo $user['status'] == 'active' ? 'مفعل' : 'غير مفعل'; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded mb-3">
                                    <div>
                                        <strong>تأكيد البريد الإلكتروني</strong>
                                        <br><small class="text-muted">حالة تأكيد بريدك الإلكتروني</small>
                                    </div>
                                    <span class="badge bg-<?php echo $user['email_verified'] ? 'success' : 'warning'; ?>">
                                        <?php echo $user['email_verified'] ? 'مؤكد' : 'غير مؤكد'; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> لتغيير الصف الدراسي أو نوع المستخدم، يرجى التواصل مع الإدارة.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التحريك
            const cards = document.querySelectorAll('.profile-card, .form-section, .stats-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
