/* 
 * ملف التصميم المخصص للموقع التعليمي
 * Custom CSS for Educational Platform
 */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 0.75rem;
}

/* إعدادات الخط الأساسي */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f8fafc;
}

/* تحسين النصوص العربية */
.arabic-text {
    font-family: 'Cairo', 'Amiri', serif;
    line-height: 1.8;
}

/* شريط التنقل */
.navbar {
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* القسم الرئيسي (Hero Section) */
.hero-section {
    background: var(--gradient-primary);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-icon {
    font-size: 15rem;
    color: rgba(255, 255, 255, 0.1);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* بطاقات الميزات */
.features-section {
    background: white;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-card h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* بطاقات الصفوف الدراسية */
.grade-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 100%;
}

.grade-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.grade-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.grade-name {
    color: var(--dark-color);
    font-weight: 600;
    margin: 0;
    font-size: 0.95rem;
}

/* عناوين الأقسام */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* الأزرار المخصصة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* التذييل */
.footer {
    background: var(--dark-color) !important;
    margin-top: auto;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 80px;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-icon {
        font-size: 8rem;
    }
    
    .feature-card {
        margin-bottom: 2rem;
    }
}

/* تحسينات إضافية للعربية */
.rtl-content {
    text-align: right;
}

/* أنماط النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* بطاقات المحتوى */
.content-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.content-card:hover {
    box-shadow: var(--shadow-md);
}

.content-card-header {
    background: var(--light-color);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.content-card-body {
    padding: 1.5rem;
}

/* شارات الحالة */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-active {
    background: #dcfce7;
    color: #166534;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

/* تحسينات الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
}

/* أنماط التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid #e2e8f0;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--light-color);
}

/* أنماط التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات الوضع الليلي */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1e293b;
        --dark-color: #f8fafc;
    }
    
    body {
        background-color: #0f172a;
        color: var(--dark-color);
    }
    
    .content-card {
        background: #1e293b;
        border-color: #334155;
    }
    
    .form-control {
        background: #1e293b;
        border-color: #334155;
        color: var(--dark-color);
    }
}

/* تحسينات إمكانية الوصول */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات التركيز */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات الطباعة */
@media print {
    .navbar,
    .footer,
    .btn {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
