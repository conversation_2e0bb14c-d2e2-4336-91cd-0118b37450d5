<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] != 'admin') {
    header('Location: ../index.php');
    exit;
}

$error = '';
$success = '';

// جلب الصفوف الدراسية
$grades = getAllGrades();

// معالجة إضافة المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $role = sanitizeInput($_POST['role']);
    $gradeId = !empty($_POST['grade_id']) ? (int)$_POST['grade_id'] : null;
    $phone = sanitizeInput($_POST['phone']);
    $status = sanitizeInput($_POST['status']);
    
    // التحقق من البيانات
    if (empty($name) || empty($email) || empty($password) || empty($role)) {
        $error = 'جميع الحقول المطلوبة يجب ملؤها';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif (!in_array($role, ['student', 'teacher', 'parent', 'admin'])) {
        $error = 'نوع المستخدم غير صحيح';
    } elseif ($role == 'student' && !$gradeId) {
        $error = 'يجب اختيار الصف الدراسي للطالب';
    } else {
        // التحقق من عدم وجود البريد الإلكتروني
        $existingUser = getUserByEmail($email);
        if ($existingUser) {
            $error = 'البريد الإلكتروني مستخدم بالفعل';
        } else {
            // إنشاء المستخدم
            $userData = [
                'name' => $name,
                'email' => $email,
                'password' => hashPassword($password),
                'role' => $role,
                'grade_id' => ($role == 'student') ? $gradeId : null,
                'phone' => $phone,
                'status' => $status,
                'email_verified' => 1 // تفعيل البريد تلقائياً للمستخدمين المضافين من المدير
            ];
            
            $newUserId = insertRecord('users', $userData);
            
            if ($newUserId) {
                $success = 'تم إضافة المستخدم بنجاح';
                
                // تسجيل النشاط
                logUserActivity($user['id'], 'create_user', "تم إنشاء مستخدم جديد: $name ($email)");
                
                // إعادة تعيين النموذج
                $name = $email = $phone = '';
                $role = 'student';
                $gradeId = null;
                $status = 'active';
            } else {
                $error = 'حدث خطأ أثناء إضافة المستخدم';
            }
        }
    }
}

// القيم الافتراضية
if (!isset($role)) $role = 'student';
if (!isset($status)) $status = 'active';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مستخدم جديد - لوحة تحكم المدير</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
        }
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar-menu .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .main-content {
            margin-right: 250px;
            padding: 2rem;
            min-height: 100vh;
            background: #f8f9fa;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e9ecef;
        }
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .section-icon {
            width: 30px;
            height: 30px;
            border-radius: 6px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }
        .password-strength {
            margin-top: 0.5rem;
        }
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            width: 0%;
        }
        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #28a745; }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>لوحة التحكم</h4>
            <p class="mb-0">مرحباً <?php echo htmlspecialchars($user['name']); ?></p>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="users.php">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="grades.php">
                        <i class="fas fa-layer-group me-2"></i>إدارة الصفوف
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="subjects.php">
                        <i class="fas fa-book me-2"></i>إدارة المواد
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="lessons.php">
                        <i class="fas fa-chalkboard-teacher me-2"></i>إدارة الدروس
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>العودة للموقع
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">إضافة مستخدم جديد</h1>
            <a href="users.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمستخدمين
            </a>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="form-card">
            <form method="POST" class="needs-validation" novalidate>
                <!-- Basic Information -->
                <div class="form-section">
                    <h5 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        المعلومات الأساسية
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required>
                                <div class="password-strength">
                                    <div class="strength-bar">
                                        <div class="strength-fill" id="strengthFill"></div>
                                    </div>
                                    <small class="text-muted" id="strengthText">قوة كلمة المرور</small>
                                </div>
                                <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role and Permissions -->
                <div class="form-section">
                    <h5 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        الدور والصلاحيات
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">نوع المستخدم <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="student" <?php echo ($role ?? '') == 'student' ? 'selected' : ''; ?>>طالب</option>
                                    <option value="teacher" <?php echo ($role ?? '') == 'teacher' ? 'selected' : ''; ?>>معلم</option>
                                    <option value="parent" <?php echo ($role ?? '') == 'parent' ? 'selected' : ''; ?>>ولي أمر</option>
                                    <option value="admin" <?php echo ($role ?? '') == 'admin' ? 'selected' : ''; ?>>مدير</option>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار نوع المستخدم</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3" id="gradeSection" style="display: <?php echo ($role ?? 'student') == 'student' ? 'block' : 'none'; ?>">
                                <label for="grade_id" class="form-label">الصف الدراسي <span class="text-danger">*</span></label>
                                <select class="form-select" id="grade_id" name="grade_id">
                                    <option value="">اختر الصف الدراسي</option>
                                    <?php foreach ($grades as $gradeOption): ?>
                                        <option value="<?php echo $gradeOption['id']; ?>" 
                                                <?php echo ($gradeId ?? '') == $gradeOption['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($gradeOption['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">يرجى اختيار الصف الدراسي للطالب</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Settings -->
                <div class="form-section">
                    <h5 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        إعدادات الحساب
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة الحساب</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($status ?? 'active') == 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($status ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم تفعيل البريد الإلكتروني تلقائياً للمستخدمين المضافين من لوحة التحكم.
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>إضافة المستخدم
                    </button>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التحقق من صحة النماذج
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // إظهار/إخفاء حقل الصف الدراسي
        document.getElementById('role').addEventListener('change', function() {
            const gradeSection = document.getElementById('gradeSection');
            const gradeSelect = document.getElementById('grade_id');
            
            if (this.value === 'student') {
                gradeSection.style.display = 'block';
                gradeSelect.required = true;
            } else {
                gradeSection.style.display = 'none';
                gradeSelect.required = false;
                gradeSelect.value = '';
            }
        });
        
        // مؤشر قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            let strength = 0;
            let text = 'ضعيفة';
            let className = 'strength-weak';
            
            if (password.length >= 6) strength += 20;
            if (password.length >= 8) strength += 20;
            if (/[A-Z]/.test(password)) strength += 20;
            if (/[0-9]/.test(password)) strength += 20;
            if (/[^A-Za-z0-9]/.test(password)) strength += 20;
            
            if (strength >= 60) {
                text = 'قوية';
                className = 'strength-strong';
            } else if (strength >= 40) {
                text = 'متوسطة';
                className = 'strength-medium';
            }
            
            strengthFill.style.width = strength + '%';
            strengthFill.className = 'strength-fill ' + className;
            strengthText.textContent = 'قوة كلمة المرور: ' + text;
        });
    </script>
</body>
</html>
