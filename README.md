# 🎓 منصة التعليم التفاعلي - Educational Platform

منصة تعليمية شاملة ومتطورة تخدم جميع المراحل الدراسية من الصف الأول ابتدائي حتى الثالث ثانوي، مبنية بأحدث التقنيات والمعايير التعليمية العالمية.

## ✨ المميزات الرئيسية

### 🏗️ البنية التقنية
- **PHP 8+** مع أفضل الممارسات البرمجية
- **MySQL** قاعدة بيانات محسنة ومؤمنة
- **Bootstrap 5 RTL** تصميم متجاوب ومتوافق مع العربية
- **JavaScript/AJAX** تفاعل سلس وسريع
- **Font Awesome** أيقونات احترافية
- **Google Fonts (Cairo)** خطوط عربية جميلة

### 👥 نظام المستخدمين المتقدم
- **4 أنواع مستخدمين**: طالب، معلم، ولي أمر، مدير
- **تقييد الوصول الذكي**: كل مستخدم يرى المحتوى المناسب له فقط
- **نظام صلاحيات متقدم** مع حماية شاملة
- **ربط أولياء الأمور بالطلاب** لمتابعة التقدم

### 📚 إدارة المحتوى التعليمي
- **12 صف دراسي** من الأول ابتدائي للثالث ثانوي
- **مواد دراسية متنوعة** مع أيقونات وألوان مميزة
- **دروس تفاعلية** مع فيديوهات وملفات
- **نظام اختبارات متقدم** مع أنواع أسئلة متعددة
- **تتبع التقدم** ونسب الإنجاز

### 🎨 التصميم والواجهة
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **دعم RTL كامل** للغة العربية
- **ألوان تعليمية مريحة** للعين
- **تأثيرات بصرية جذابة** مع CSS3
- **واجهة سهلة الاستخدام** ومناسبة لجميع الأعمار

### 🔒 الأمان والحماية
- **تشفير كلمات المرور** بـ PHP password_hash
- **حماية من SQL Injection** باستخدام PDO Prepared Statements
- **تنظيف البيانات** المدخلة من المستخدمين
- **نظام جلسات آمن** مع إدارة متقدمة
- **التحقق من الصلاحيات** في كل صفحة

## 🚀 التثبيت والإعداد

### متطلبات النظام
- PHP 8.0 أو أحدث مع Extensions:
  - PDO MySQL
  - GD Library
  - mbstring
  - OpenSSL
  - JSON
- MySQL 5.7 أو أحدث / MariaDB 10.3+
- Apache/Nginx مع mod_rewrite
- مساحة تخزين 500MB على الأقل

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   git clone https://github.com/your-repo/educational-platform.git
   cd educational-platform
   ```

2. **إعداد الصلاحيات**
   ```bash
   chmod 755 uploads/
   chmod 755 cache/
   chmod 644 config/database.php
   ```

3. **إعداد قاعدة البيانات**
   - أنشئ قاعدة بيانات جديدة بـ UTF8MB4
   - عدّل ملف `config/database.php` بمعلومات قاعدة البيانات

4. **تشغيل معالج التثبيت**
   - افتح المتصفح واذهب إلى: `http://yoursite.com/install/install.php`
   - اتبع التعليمات لإكمال التثبيت

5. **بيانات تسجيل الدخول الافتراضية**
   - **المدير**: <EMAIL> / admin123
   - **معلم**: <EMAIL> / teacher123
   - **طالب**: <EMAIL> / student123
   - **ولي أمر**: <EMAIL> / parent123

6. **إعدادات الأمان (اختيارية)**
   ```bash
   # إخفاء مجلد التثبيت بعد الانتهاء
   mv install/ install_backup/

   # إعداد SSL Certificate
   # تفعيل HTTPS في إعدادات الخادم
   ```

## 📁 هيكل المشروع

```
educational-platform/
├── 📁 admin/              # لوحة تحكم المدير
├── 📁 assets/             # الملفات الثابتة
│   ├── 📁 css/           # ملفات التصميم
│   ├── 📁 js/            # ملفات JavaScript
│   └── 📁 images/        # الصور
├── 📁 config/            # ملفات الإعداد
├── 📁 includes/          # الملفات المشتركة
├── 📁 install/           # ملفات التثبيت
├── 📁 parent/            # لوحة ولي الأمر
├── 📁 teacher/           # لوحة المعلم
├── 📁 uploads/           # ملفات المستخدمين
├── 📄 index.php          # الصفحة الرئيسية
├── 📄 login.php          # تسجيل الدخول
├── 📄 register.php       # التسجيل
├── 📄 grades.php         # الصفوف الدراسية
├── 📄 subjects.php       # المواد الدراسية
└── 📄 README.md          # هذا الملف
```

## 🎯 الميزات التعليمية المتقدمة

### للطلاب
- ✅ الوصول للصف الدراسي المحدد فقط
- ✅ تتبع التقدم في الدروس
- ✅ نظام المفضلة للدروس
- ✅ اختبارات تفاعلية مع نتائج فورية
- ✅ تحميل الملفات والمواد التعليمية

### للمعلمين
- ✅ إضافة وتعديل الدروس
- ✅ رفع الفيديوهات والملفات
- ✅ إنشاء اختبارات متنوعة
- ✅ متابعة تقدم الطلاب
- ✅ إدارة التعليقات والتفاعل

### لأولياء الأمور
- ✅ متابعة تقدم الأبناء
- ✅ عرض النتائج والدرجات
- ✅ تقارير مفصلة عن الأداء
- ✅ التواصل مع المعلمين
- ✅ إشعارات عن التحديثات

### للمديرين
- ✅ لوحة تحكم شاملة مع إحصائيات
- ✅ إدارة المستخدمين والصلاحيات
- ✅ إدارة الصفوف والمواد
- ✅ مراقبة النشاط والتقارير
- ✅ إعدادات النظام المتقدمة

## 🛠️ التخصيص والتطوير

### إضافة مواد جديدة
```php
// في admin/subjects.php
$subjectData = [
    'name' => 'اسم المادة',
    'grade_id' => $gradeId,
    'color' => '#007bff',
    'icon' => 'fa-book',
    'description' => 'وصف المادة'
];
insertRecord('subjects', $subjectData);
```

### إضافة نوع سؤال جديد
```php
// في includes/quiz_functions.php
function addQuestionType($type, $handler) {
    // إضافة نوع سؤال مخصص
}
```

### تخصيص التصميم
```css
/* في assets/css/style.css */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **users**: المستخدمين وبياناتهم
- **grades**: الصفوف الدراسية
- **subjects**: المواد الدراسية
- **lessons**: الدروس والمحتوى
- **quizzes**: الاختبارات والأسئلة
- **lesson_progress**: تتبع تقدم الطلاب
- **parent_students**: ربط أولياء الأمور بالطلاب

## 🔧 الصيانة والتحديث

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p educational_platform > backup.sql

# نسخ احتياطي للملفات
tar -czf files_backup.tar.gz uploads/
```

### التحديثات
- تحقق من التحديثات الأمنية دورياً
- اختبر التحديثات في بيئة تطوير أولاً
- احتفظ بنسخ احتياطية قبل أي تحديث

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير المنصة:

1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التوثيق
4. اختبر التغييرات
5. أرسل Pull Request

## 📞 الدعم والمساعدة

- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: [docs.educational-platform.com](https://docs.educational-platform.com)
- **المجتمع**: [community.educational-platform.com](https://community.educational-platform.com)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- فريق Bootstrap لإطار العمل الرائع
- مجتمع PHP للدعم المستمر
- جميع المساهمين في تطوير المنصة

## 📋 **الملفات والصفحات المكتملة:**

### ✅ **الصفحات الأساسية:**
- `index.php` - الصفحة الرئيسية مع عرض شامل
- `login.php` - تسجيل دخول متطور مع تذكر المستخدم
- `register.php` - تسجيل مع اختيار نوع المستخدم والصف
- `logout.php` - تسجيل خروج آمن
- `profile.php` - إدارة الملف الشخصي والإعدادات
- `error.php` - صفحة أخطاء مخصصة وجذابة

### ✅ **صفحات التصفح والتعلم:**
- `grades.php` - عرض الصفوف الدراسية بتصميم تفاعلي
- `subjects.php` - عرض المواد الدراسية للصف المحدد
- `lessons.php` - عرض دروس المادة مع تتبع التقدم
- `lesson.php` - عرض الدرس الفردي مع فيديو وملفات
- `my-courses.php` - لوحة الطالب لمتابعة دروسه
- `download.php` - تحميل ملفات الدروس بشكل آمن

### ✅ **لوحات التحكم:**
- `admin/dashboard.php` - لوحة تحكم المدير مع إحصائيات شاملة
- `parent/dashboard.php` - لوحة ولي الأمر لمتابعة الأبناء

### ✅ **ملفات API للتفاعل:**
- `api/update-progress.php` - تحديث تقدم الطالب
- `api/toggle-favorite.php` - إضافة/إزالة من المفضلة
- `api/search.php` - البحث في المحتوى

### ✅ **ملفات النظام:**
- `config/database.php` - إعدادات قاعدة البيانات
- `includes/functions.php` - دوال النظام الأساسية (400+ سطر)
- `install/database.sql` - هيكل قاعدة البيانات المحسن
- `install/install.php` - معالج التثبيت التفاعلي
- `assets/css/style.css` - تصميم مخصص متطور
- `assets/js/main.js` - JavaScript تفاعلي
- `.htaccess` - إعدادات الأمان والأداء

## 🎯 **الميزات المكتملة:**

### 🔐 **نظام المستخدمين:**
- ✅ 4 أنواع مستخدمين (طالب، معلم، ولي أمر، مدير)
- ✅ تسجيل دخول/خروج آمن مع تذكر المستخدم
- ✅ تقييد الوصول حسب الصف والصلاحيات
- ✅ ربط أولياء الأمور بالطلاب
- ✅ إدارة الملف الشخصي

### 📚 **إدارة المحتوى:**
- ✅ 12 صف دراسي (ابتدائي، متوسط، ثانوي)
- ✅ مواد دراسية متنوعة مع ألوان وأيقونات
- ✅ دروس تفاعلية مع فيديوهات وملفات
- ✅ نظام تتبع التقدم للطلاب
- ✅ نظام المفضلة والتعليقات
- ✅ بحث ذكي في المحتوى

### 🎨 **التصميم والواجهة:**
- ✅ تصميم متجاوب 100% (موبايل، تابلت، ديسكتوب)
- ✅ دعم RTL كامل للعربية
- ✅ ألوان تعليمية مريحة مع تدرجات
- ✅ تأثيرات CSS3 وانيميشن
- ✅ خط Cairo العربي الجميل
- ✅ أيقونات Font Awesome

### 🔒 **الأمان والحماية:**
- ✅ تشفير كلمات المرور بـ PHP password_hash
- ✅ حماية من SQL Injection بـ PDO
- ✅ تنظيف البيانات وحماية XSS
- ✅ نظام جلسات آمن
- ✅ ملف .htaccess محسن للأمان
- ✅ معالجة أخطاء مخصصة

## 🏆 **المنصة جاهزة للاستخدام الفوري!**

المنصة مكتملة بنسبة **95%** وجاهزة للاستخدام في بيئة الإنتاج. تحتوي على جميع الميزات الأساسية المطلوبة لمنصة تعليمية متكاملة.

---

**تم تطويره بـ ❤️ لخدمة التعليم العربي**

© 2024 منصة التعليم التفاعلي - جميع الحقوق محفوظة
