<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] != 'student') {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['lesson_id']) || !isset($input['progress'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit;
}

$lessonId = (int)$input['lesson_id'];
$progress = (float)$input['progress'];

// التحقق من صحة البيانات
if ($lessonId <= 0 || $progress < 0 || $progress > 100) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

// التحقق من وجود الدرس والصلاحية للوصول إليه
$lesson = getLessonById($lessonId);
if (!$lesson) {
    echo json_encode(['success' => false, 'message' => 'الدرس غير موجود']);
    exit;
}

// التحقق من صلاحية الوصول للصف
$subjectData = fetchOne("SELECT grade_id FROM subjects WHERE id = :id", ['id' => $lesson['subject_id']]);
if (!canAccessGrade($user['id'], $subjectData['grade_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول لهذا الدرس']);
    exit;
}

try {
    // تسجيل أو تحديث التقدم
    $result = recordLessonProgress($user['id'], $lessonId, $progress);
    
    if ($result) {
        $response = [
            'success' => true,
            'message' => 'تم تحديث التقدم بنجاح',
            'progress' => $progress
        ];
        
        // إضافة رسالة خاصة عند الإكمال
        if ($progress >= 100) {
            $response['message'] = 'مبروك! تم إكمال الدرس بنجاح';
            $response['completed'] = true;
        }
        
        echo json_encode($response);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث التقدم']);
    }
} catch (Exception $e) {
    error_log("Progress update error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
