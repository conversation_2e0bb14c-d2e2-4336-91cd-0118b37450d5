<?php
/**
 * ملف الدوال الأساسية للموقع التعليمي
 * Main Functions File for Educational Platform
 */

/**
 * دالة تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * دالة التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * دالة تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة جلب بيانات المستخدم بالمعرف
 */
function getUserById($userId) {
    $sql = "SELECT * FROM users WHERE id = :id AND status = 'active'";
    return fetchOne($sql, ['id' => $userId]);
}

/**
 * دالة جلب بيانات المستخدم بالبريد الإلكتروني
 */
function getUserByEmail($email) {
    $sql = "SELECT * FROM users WHERE email = :email AND status = 'active'";
    return fetchOne($sql, ['email' => $email]);
}

/**
 * دالة إنشاء مستخدم جديد
 */
function createUser($data) {
    // تشفير كلمة المرور
    $data['password'] = hashPassword($data['password']);
    $data['created_at'] = date('Y-m-d H:i:s');
    $data['status'] = 'active';
    
    return insertRecord('users', $data);
}

/**
 * دالة التحقق من صلاحية المستخدم للوصول للصف
 */
function canAccessGrade($userId, $gradeId) {
    $user = getUserById($userId);
    
    if (!$user) return false;
    
    // المدير يمكنه الوصول لكل شيء
    if ($user['role'] == 'admin') return true;
    
    // المعلم يمكنه الوصول لكل شيء
    if ($user['role'] == 'teacher') return true;
    
    // الطالب يمكنه الوصول لصفه فقط
    if ($user['role'] == 'student') {
        return $user['grade_id'] == $gradeId;
    }
    
    // ولي الأمر يمكنه الوصول لصفوف أبنائه
    if ($user['role'] == 'parent') {
        $sql = "SELECT COUNT(*) as count FROM parent_students ps 
                JOIN users u ON ps.student_id = u.id 
                WHERE ps.parent_id = :parent_id AND u.grade_id = :grade_id";
        $result = fetchOne($sql, ['parent_id' => $userId, 'grade_id' => $gradeId]);
        return $result['count'] > 0;
    }
    
    return false;
}

/**
 * دالة جلب جميع الصفوف الدراسية
 */
function getAllGrades() {
    $sql = "SELECT * FROM grades ORDER BY level ASC, name ASC";
    return fetchAll($sql);
}

/**
 * دالة جلب الصفوف المتاحة للمستخدم
 */
function getAvailableGrades($userId) {
    $user = getUserById($userId);
    
    if (!$user) return [];
    
    // المدير والمعلم يرون كل الصفوف
    if (in_array($user['role'], ['admin', 'teacher'])) {
        return getAllGrades();
    }
    
    // الطالب يرى صفه فقط
    if ($user['role'] == 'student') {
        $sql = "SELECT g.* FROM grades g WHERE g.id = :grade_id";
        return fetchAll($sql, ['grade_id' => $user['grade_id']]);
    }
    
    // ولي الأمر يرى صفوف أبنائه
    if ($user['role'] == 'parent') {
        $sql = "SELECT DISTINCT g.* FROM grades g 
                JOIN users u ON g.id = u.grade_id 
                JOIN parent_students ps ON u.id = ps.student_id 
                WHERE ps.parent_id = :parent_id 
                ORDER BY g.level ASC, g.name ASC";
        return fetchAll($sql, ['parent_id' => $userId]);
    }
    
    return [];
}

/**
 * دالة جلب المواد الدراسية للصف
 */
function getSubjectsByGrade($gradeId) {
    $sql = "SELECT * FROM subjects WHERE grade_id = :grade_id ORDER BY name ASC";
    return fetchAll($sql, ['grade_id' => $gradeId]);
}

/**
 * دالة جلب الدروس للمادة
 */
function getLessonsBySubject($subjectId) {
    $sql = "SELECT * FROM lessons WHERE subject_id = :subject_id AND status = 'published' ORDER BY order_num ASC, created_at ASC";
    return fetchAll($sql, ['subject_id' => $subjectId]);
}

/**
 * دالة جلب درس بالمعرف
 */
function getLessonById($lessonId) {
    $sql = "SELECT l.*, s.name as subject_name, g.name as grade_name 
            FROM lessons l 
            JOIN subjects s ON l.subject_id = s.id 
            JOIN grades g ON s.grade_id = g.id 
            WHERE l.id = :id AND l.status = 'published'";
    return fetchOne($sql, ['id' => $lessonId]);
}

/**
 * دالة تسجيل تقدم الطالب في الدرس
 */
function recordLessonProgress($userId, $lessonId, $progress = 100) {
    // التحقق من وجود سجل سابق
    $sql = "SELECT id FROM lesson_progress WHERE user_id = :user_id AND lesson_id = :lesson_id";
    $existing = fetchOne($sql, ['user_id' => $userId, 'lesson_id' => $lessonId]);
    
    if ($existing) {
        // تحديث السجل الموجود
        $data = [
            'progress' => $progress,
            'completed_at' => $progress >= 100 ? date('Y-m-d H:i:s') : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        return updateRecord('lesson_progress', $data, 'id = :id', ['id' => $existing['id']]);
    } else {
        // إنشاء سجل جديد
        $data = [
            'user_id' => $userId,
            'lesson_id' => $lessonId,
            'progress' => $progress,
            'completed_at' => $progress >= 100 ? date('Y-m-d H:i:s') : null,
            'created_at' => date('Y-m-d H:i:s')
        ];
        return insertRecord('lesson_progress', $data);
    }
}

/**
 * دالة جلب تقدم الطالب في الدرس
 */
function getLessonProgress($userId, $lessonId) {
    $sql = "SELECT * FROM lesson_progress WHERE user_id = :user_id AND lesson_id = :lesson_id";
    return fetchOne($sql, ['user_id' => $userId, 'lesson_id' => $lessonId]);
}

/**
 * دالة جلب إحصائيات المستخدم
 */
function getUserStats($userId) {
    $stats = [];
    
    // عدد الدروس المكتملة
    $sql = "SELECT COUNT(*) as completed_lessons FROM lesson_progress WHERE user_id = :user_id AND progress >= 100";
    $result = fetchOne($sql, ['user_id' => $userId]);
    $stats['completed_lessons'] = $result['completed_lessons'];
    
    // عدد الدروس قيد التقدم
    $sql = "SELECT COUNT(*) as in_progress_lessons FROM lesson_progress WHERE user_id = :user_id AND progress > 0 AND progress < 100";
    $result = fetchOne($sql, ['user_id' => $userId]);
    $stats['in_progress_lessons'] = $result['in_progress_lessons'];
    
    // متوسط التقدم
    $sql = "SELECT AVG(progress) as avg_progress FROM lesson_progress WHERE user_id = :user_id";
    $result = fetchOne($sql, ['user_id' => $userId]);
    $stats['avg_progress'] = round($result['avg_progress'], 2);
    
    return $stats;
}

/**
 * دالة إنشاء رمز التحقق العشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * دالة إرسال بريد إلكتروني
 */
function sendEmail($to, $subject, $message, $headers = '') {
    // يمكن تطوير هذه الدالة لاستخدام مكتبة PHPMailer أو خدمة بريد خارجية
    return mail($to, $subject, $message, $headers);
}

/**
 * دالة تحويل التاريخ للعربية
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * دالة تحديد نوع الملف
 */
function getFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    $types = [
        'pdf' => 'PDF',
        'doc' => 'Word',
        'docx' => 'Word',
        'ppt' => 'PowerPoint',
        'pptx' => 'PowerPoint',
        'xls' => 'Excel',
        'xlsx' => 'Excel',
        'jpg' => 'صورة',
        'jpeg' => 'صورة',
        'png' => 'صورة',
        'gif' => 'صورة',
        'mp4' => 'فيديو',
        'avi' => 'فيديو',
        'mov' => 'فيديو',
        'mp3' => 'صوت',
        'wav' => 'صوت'
    ];
    
    return isset($types[$extension]) ? $types[$extension] : 'ملف';
}

/**
 * دالة تحويل حجم الملف
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
