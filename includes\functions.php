<?php
/**
 * ملف الدوال الأساسية للموقع التعليمي
 * Main Functions File for Educational Platform
 */

/**
 * دالة تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * دالة التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * دالة تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة جلب بيانات المستخدم بالمعرف
 */
function getUserById($userId) {
    $sql = "SELECT * FROM users WHERE id = :id AND status = 'active'";
    return fetchOne($sql, ['id' => $userId]);
}

/**
 * دالة جلب بيانات المستخدم بالبريد الإلكتروني
 */
function getUserByEmail($email) {
    $sql = "SELECT * FROM users WHERE email = :email AND status = 'active'";
    return fetchOne($sql, ['email' => $email]);
}

/**
 * دالة إنشاء مستخدم جديد
 */
function createUser($data) {
    // تشفير كلمة المرور
    $data['password'] = hashPassword($data['password']);
    $data['created_at'] = date('Y-m-d H:i:s');
    $data['status'] = 'active';
    
    return insertRecord('users', $data);
}

/**
 * دالة التحقق من صلاحية المستخدم للوصول للصف
 */
function canAccessGrade($userId, $gradeId) {
    $user = getUserById($userId);
    
    if (!$user) return false;
    
    // المدير يمكنه الوصول لكل شيء
    if ($user['role'] == 'admin') return true;
    
    // المعلم يمكنه الوصول لكل شيء
    if ($user['role'] == 'teacher') return true;
    
    // الطالب يمكنه الوصول لصفه فقط
    if ($user['role'] == 'student') {
        return $user['grade_id'] == $gradeId;
    }
    
    // ولي الأمر يمكنه الوصول لصفوف أبنائه
    if ($user['role'] == 'parent') {
        $sql = "SELECT COUNT(*) as count FROM parent_students ps 
                JOIN users u ON ps.student_id = u.id 
                WHERE ps.parent_id = :parent_id AND u.grade_id = :grade_id";
        $result = fetchOne($sql, ['parent_id' => $userId, 'grade_id' => $gradeId]);
        return $result['count'] > 0;
    }
    
    return false;
}

/**
 * دالة جلب جميع الصفوف الدراسية
 */
function getAllGrades() {
    $sql = "SELECT * FROM grades ORDER BY level ASC, name ASC";
    return fetchAll($sql);
}

/**
 * دالة جلب الصفوف المتاحة للمستخدم
 */
function getAvailableGrades($userId) {
    $user = getUserById($userId);
    
    if (!$user) return [];
    
    // المدير والمعلم يرون كل الصفوف
    if (in_array($user['role'], ['admin', 'teacher'])) {
        return getAllGrades();
    }
    
    // الطالب يرى صفه فقط
    if ($user['role'] == 'student') {
        $sql = "SELECT g.* FROM grades g WHERE g.id = :grade_id";
        return fetchAll($sql, ['grade_id' => $user['grade_id']]);
    }
    
    // ولي الأمر يرى صفوف أبنائه
    if ($user['role'] == 'parent') {
        $sql = "SELECT DISTINCT g.* FROM grades g 
                JOIN users u ON g.id = u.grade_id 
                JOIN parent_students ps ON u.id = ps.student_id 
                WHERE ps.parent_id = :parent_id 
                ORDER BY g.level ASC, g.name ASC";
        return fetchAll($sql, ['parent_id' => $userId]);
    }
    
    return [];
}

/**
 * دالة جلب المواد الدراسية للصف
 */
function getSubjectsByGrade($gradeId) {
    $sql = "SELECT * FROM subjects WHERE grade_id = :grade_id ORDER BY name ASC";
    return fetchAll($sql, ['grade_id' => $gradeId]);
}

/**
 * دالة جلب الدروس للمادة
 */
function getLessonsBySubject($subjectId) {
    $sql = "SELECT * FROM lessons WHERE subject_id = :subject_id AND status = 'published' ORDER BY order_num ASC, created_at ASC";
    return fetchAll($sql, ['subject_id' => $subjectId]);
}

/**
 * دالة جلب درس بالمعرف
 */
function getLessonById($lessonId) {
    $sql = "SELECT l.*, s.name as subject_name, g.name as grade_name 
            FROM lessons l 
            JOIN subjects s ON l.subject_id = s.id 
            JOIN grades g ON s.grade_id = g.id 
            WHERE l.id = :id AND l.status = 'published'";
    return fetchOne($sql, ['id' => $lessonId]);
}

/**
 * دالة تسجيل تقدم الطالب في الدرس
 */
function recordLessonProgress($userId, $lessonId, $progress = 100) {
    // التحقق من وجود سجل سابق
    $sql = "SELECT id FROM lesson_progress WHERE user_id = :user_id AND lesson_id = :lesson_id";
    $existing = fetchOne($sql, ['user_id' => $userId, 'lesson_id' => $lessonId]);
    
    if ($existing) {
        // تحديث السجل الموجود
        $data = [
            'progress' => $progress,
            'completed_at' => $progress >= 100 ? date('Y-m-d H:i:s') : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        return updateRecord('lesson_progress', $data, 'id = :id', ['id' => $existing['id']]);
    } else {
        // إنشاء سجل جديد
        $data = [
            'user_id' => $userId,
            'lesson_id' => $lessonId,
            'progress' => $progress,
            'completed_at' => $progress >= 100 ? date('Y-m-d H:i:s') : null,
            'created_at' => date('Y-m-d H:i:s')
        ];
        return insertRecord('lesson_progress', $data);
    }
}

/**
 * دالة جلب تقدم الطالب في الدرس
 */
function getLessonProgress($userId, $lessonId) {
    $sql = "SELECT * FROM lesson_progress WHERE user_id = :user_id AND lesson_id = :lesson_id";
    return fetchOne($sql, ['user_id' => $userId, 'lesson_id' => $lessonId]);
}

/**
 * دالة جلب إحصائيات المستخدم
 */
function getUserStats($userId) {
    $stats = [];
    
    // عدد الدروس المكتملة
    $sql = "SELECT COUNT(*) as completed_lessons FROM lesson_progress WHERE user_id = :user_id AND progress >= 100";
    $result = fetchOne($sql, ['user_id' => $userId]);
    $stats['completed_lessons'] = $result['completed_lessons'];
    
    // عدد الدروس قيد التقدم
    $sql = "SELECT COUNT(*) as in_progress_lessons FROM lesson_progress WHERE user_id = :user_id AND progress > 0 AND progress < 100";
    $result = fetchOne($sql, ['user_id' => $userId]);
    $stats['in_progress_lessons'] = $result['in_progress_lessons'];
    
    // متوسط التقدم
    $sql = "SELECT AVG(progress) as avg_progress FROM lesson_progress WHERE user_id = :user_id";
    $result = fetchOne($sql, ['user_id' => $userId]);
    $stats['avg_progress'] = round($result['avg_progress'], 2);
    
    return $stats;
}

/**
 * دالة إنشاء رمز التحقق العشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * دالة إرسال بريد إلكتروني
 */
function sendEmail($to, $subject, $message, $headers = '') {
    // يمكن تطوير هذه الدالة لاستخدام مكتبة PHPMailer أو خدمة بريد خارجية
    return mail($to, $subject, $message, $headers);
}

/**
 * دالة تحويل التاريخ للعربية
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * دالة تحديد نوع الملف
 */
function getFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    $types = [
        'pdf' => 'PDF',
        'doc' => 'Word',
        'docx' => 'Word',
        'ppt' => 'PowerPoint',
        'pptx' => 'PowerPoint',
        'xls' => 'Excel',
        'xlsx' => 'Excel',
        'jpg' => 'صورة',
        'jpeg' => 'صورة',
        'png' => 'صورة',
        'gif' => 'صورة',
        'mp4' => 'فيديو',
        'avi' => 'فيديو',
        'mov' => 'فيديو',
        'mp3' => 'صوت',
        'wav' => 'صوت'
    ];
    
    return isset($types[$extension]) ? $types[$extension] : 'ملف';
}

/**
 * دالة تحويل حجم الملف
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * دالة إنشاء إشعار للمستخدم
 */
function createNotification($userId, $title, $message, $type = 'info', $actionUrl = null) {
    $data = [
        'user_id' => $userId,
        'title' => $title,
        'message' => $message,
        'type' => $type,
        'action_url' => $actionUrl,
        'is_read' => 0
    ];

    return insertRecord('notifications', $data);
}

/**
 * دالة جلب الإشعارات غير المقروءة للمستخدم
 */
function getUnreadNotifications($userId, $limit = 10) {
    $sql = "SELECT * FROM notifications WHERE user_id = :user_id AND is_read = 0 ORDER BY created_at DESC LIMIT :limit";
    $stmt = executeQuery($sql, ['user_id' => $userId]);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    return $stmt ? $stmt->fetchAll() : [];
}

/**
 * دالة تسجيل نشاط المستخدم
 */
function logUserActivity($userId, $action, $details = null) {
    $data = [
        'user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ];

    return insertRecord('user_activities', $data);
}

/**
 * دالة التحقق من صلاحية رفع الملف
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 10485760) {
    $errors = [];

    // التحقق من وجود خطأ في الرفع
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = 'حدث خطأ أثناء رفع الملف';
        return $errors;
    }

    // التحقق من حجم الملف
    if ($file['size'] > $maxSize) {
        $errors[] = 'حجم الملف كبير جداً. الحد الأقصى: ' . formatFileSize($maxSize);
    }

    // التحقق من نوع الملف
    if (!empty($allowedTypes)) {
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $allowedTypes)) {
            $errors[] = 'نوع الملف غير مسموح. الأنواع المسموحة: ' . implode(', ', $allowedTypes);
        }
    }

    // التحقق من أن الملف ليس ملف تنفيذي
    $dangerousExtensions = ['php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'];
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (in_array($fileExtension, $dangerousExtensions)) {
        $errors[] = 'نوع الملف غير آمن';
    }

    return $errors;
}

/**
 * دالة رفع الملف بشكل آمن
 */
function uploadFile($file, $uploadDir = 'uploads/', $newName = null) {
    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // تحديد اسم الملف
    if ($newName) {
        $fileName = $newName;
    } else {
        $fileName = uniqid() . '_' . time() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
    }

    $filePath = $uploadDir . $fileName;

    // نقل الملف
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return [
            'success' => true,
            'file_path' => $filePath,
            'file_name' => $fileName,
            'original_name' => $file['name'],
            'file_size' => $file['size']
        ];
    } else {
        return [
            'success' => false,
            'error' => 'فشل في رفع الملف'
        ];
    }
}

/**
 * دالة إنشاء صورة مصغرة
 */
function createThumbnail($sourcePath, $thumbnailPath, $width = 300, $height = 200) {
    $imageInfo = getimagesize($sourcePath);
    if (!$imageInfo) {
        return false;
    }

    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $imageType = $imageInfo[2];

    // إنشاء الصورة المصدر
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }

    // إنشاء الصورة المصغرة
    $thumbnail = imagecreatetruecolor($width, $height);

    // الحفاظ على الشفافية للـ PNG
    if ($imageType == IMAGETYPE_PNG) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
    }

    // تغيير حجم الصورة
    imagecopyresampled($thumbnail, $sourceImage, 0, 0, 0, 0, $width, $height, $sourceWidth, $sourceHeight);

    // حفظ الصورة المصغرة
    $result = false;
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumbnail, $thumbnailPath, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($thumbnail, $thumbnailPath);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumbnail, $thumbnailPath);
            break;
    }

    // تنظيف الذاكرة
    imagedestroy($sourceImage);
    imagedestroy($thumbnail);

    return $result;
}

/**
 * دالة تنظيف النص من HTML
 */
function stripHtmlTags($text, $allowedTags = '') {
    return strip_tags($text, $allowedTags);
}

/**
 * دالة اختصار النص
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }

    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * دالة التحقق من قوة كلمة المرور
 */
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];

    // طول كلمة المرور
    if (strlen($password) >= 8) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    }

    // وجود أحرف كبيرة
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على حرف كبير واحد على الأقل';
    }

    // وجود أحرف صغيرة
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على حرف صغير واحد على الأقل';
    }

    // وجود أرقام
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على رقم واحد على الأقل';
    }

    // وجود رموز خاصة
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على رمز خاص واحد على الأقل';
    }

    $strength = 'ضعيفة';
    if ($score >= 4) {
        $strength = 'قوية';
    } elseif ($score >= 3) {
        $strength = 'متوسطة';
    }

    return [
        'score' => $score,
        'strength' => $strength,
        'feedback' => $feedback
    ];
}

/**
 * دالة تحويل الوقت النسبي (منذ كم من الوقت)
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return "منذ $minutes دقيقة";
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return "منذ $hours ساعة";
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return "منذ $days يوم";
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return "منذ $months شهر";
    } else {
        $years = floor($time / 31536000);
        return "منذ $years سنة";
    }
}

/**
 * دالة إنشاء رابط آمن مع CSRF token
 */
function createSecureUrl($url, $params = []) {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken(32);
    }

    $params['csrf_token'] = $_SESSION['csrf_token'];

    return $url . '?' . http_build_query($params);
}

/**
 * دالة التحقق من CSRF token
 */
function verifyCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
?>
