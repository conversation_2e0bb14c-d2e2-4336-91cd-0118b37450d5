<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من معرف المادة
if (!isset($_GET['subject']) || !is_numeric($_GET['subject'])) {
    header('Location: grades.php');
    exit;
}

$subjectId = (int)$_GET['subject'];

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// جلب بيانات المادة والصف
$subject = fetchOne("
    SELECT s.*, g.name as grade_name, g.color as grade_color, g.icon as grade_icon 
    FROM subjects s 
    JOIN grades g ON s.grade_id = g.id 
    WHERE s.id = :id
", ['id' => $subjectId]);

if (!$subject) {
    header('Location: grades.php');
    exit;
}

// التحقق من صلاحية الوصول للصف
if ($user && !canAccessGrade($user['id'], $subject['grade_id'])) {
    header('Location: grades.php');
    exit;
}

// جلب الاختبارات للمادة
$quizzes = fetchAll("
    SELECT q.*, 
           COUNT(qq.id) as questions_count,
           AVG(qa.score) as avg_score,
           COUNT(DISTINCT qa.user_id) as attempts_count
    FROM quizzes q
    LEFT JOIN quiz_questions qq ON q.id = qq.quiz_id
    LEFT JOIN quiz_attempts qa ON q.id = qa.quiz_id AND qa.status = 'completed'
    WHERE q.subject_id = :subject_id AND q.status = 'published'
    GROUP BY q.id
    ORDER BY q.order_num ASC, q.created_at DESC
", ['subject_id' => $subjectId]);

// جلب محاولات المستخدم إذا كان مسجل دخوله
$userAttempts = [];
if ($user && $user['role'] == 'student') {
    foreach ($quizzes as $quiz) {
        $attempt = fetchOne("
            SELECT * FROM quiz_attempts 
            WHERE quiz_id = :quiz_id AND user_id = :user_id 
            ORDER BY created_at DESC LIMIT 1
        ", ['quiz_id' => $quiz['id'], 'user_id' => $user['id']]);
        
        if ($attempt) {
            $userAttempts[$quiz['id']] = $attempt;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبارات <?php echo htmlspecialchars($subject['name']); ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, <?php echo $subject['color']; ?> 0%, <?php echo $subject['color']; ?>CC 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .quiz-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            overflow: hidden;
            height: 100%;
            position: relative;
        }
        .quiz-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: <?php echo $subject['color']; ?>;
        }
        .quiz-header {
            background: linear-gradient(135deg, <?php echo $subject['color']; ?>20 0%, <?php echo $subject['color']; ?>40 100%);
            padding: 1.5rem;
            position: relative;
        }
        .quiz-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: <?php echo $subject['color']; ?>;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
        .quiz-content {
            padding: 1.5rem;
        }
        .quiz-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }
        .quiz-description {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        .quiz-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--secondary-color);
        }
        .quiz-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: <?php echo $subject['color']; ?>;
        }
        .stat-label {
            font-size: 0.8rem;
            color: var(--secondary-color);
        }
        .quiz-badges {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        .quiz-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .badge-difficulty-easy { background: #d4edda; color: #155724; }
        .badge-difficulty-medium { background: #fff3cd; color: #856404; }
        .badge-difficulty-hard { background: #f8d7da; color: #721c24; }
        .badge-time { background: #e3f2fd; color: #1976d2; }
        .badge-questions { background: #f3e5f5; color: #7b1fa2; }
        .attempt-status {
            position: absolute;
            top: 1rem;
            left: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-completed { background: #d4edda; color: #155724; }
        .status-in-progress { background: #fff3cd; color: #856404; }
        .status-not-started { background: #e2e3e5; color: #495057; }
        .score-display {
            background: <?php echo $subject['color']; ?>;
            color: white;
            padding: 0.75rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 1rem;
        }
        .score-number {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--secondary-color);
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        .subject-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .subject-icon-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1.5rem;
            margin-left: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php" class="text-white-50">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="grades.php" class="text-white-50">الصفوف الدراسية</a></li>
                    <li class="breadcrumb-item"><a href="subjects.php?grade=<?php echo $subject['grade_id']; ?>" class="text-white-50"><?php echo htmlspecialchars($subject['grade_name']); ?></a></li>
                    <li class="breadcrumb-item"><a href="lessons.php?subject=<?php echo $subject['id']; ?>" class="text-white-50"><?php echo htmlspecialchars($subject['name']); ?></a></li>
                    <li class="breadcrumb-item active text-white">الاختبارات</li>
                </ol>
            </nav>
            
            <div class="subject-info">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="subject-icon-large">
                            <i class="fas fa-question-circle"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h1 class="display-5 fw-bold mb-2">اختبارات <?php echo htmlspecialchars($subject['name']); ?></h1>
                        <p class="lead mb-0"><?php echo htmlspecialchars($subject['grade_name']); ?></p>
                        <p class="mb-0 mt-2">اختبر معلوماتك وقيس مستوى فهمك للمادة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <?php if (!empty($quizzes)): ?>
                <div class="row g-4">
                    <?php foreach ($quizzes as $quiz): ?>
                        <?php
                        $userAttempt = $userAttempts[$quiz['id']] ?? null;
                        $canTakeQuiz = !$user || $user['role'] != 'student' || !$userAttempt || $quiz['allow_retake'];
                        ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="quiz-card">
                                <?php if ($userAttempt): ?>
                                    <div class="attempt-status status-<?php echo $userAttempt['status'] == 'completed' ? 'completed' : 'in-progress'; ?>">
                                        <?php if ($userAttempt['status'] == 'completed'): ?>
                                            <i class="fas fa-check me-1"></i>مكتمل
                                        <?php else: ?>
                                            <i class="fas fa-clock me-1"></i>قيد التقدم
                                        <?php endif; ?>
                                    </div>
                                <?php elseif ($user && $user['role'] == 'student'): ?>
                                    <div class="attempt-status status-not-started">
                                        <i class="fas fa-play me-1"></i>لم يبدأ
                                    </div>
                                <?php endif; ?>
                                
                                <div class="quiz-header">
                                    <div class="quiz-icon">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                </div>
                                
                                <div class="quiz-content">
                                    <h3 class="quiz-title"><?php echo htmlspecialchars($quiz['title']); ?></h3>
                                    
                                    <?php if ($quiz['description']): ?>
                                        <p class="quiz-description"><?php echo htmlspecialchars($quiz['description']); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="quiz-badges">
                                        <span class="quiz-badge badge-difficulty-<?php echo $quiz['difficulty']; ?>">
                                            <?php 
                                            $difficulties = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                            echo $difficulties[$quiz['difficulty']];
                                            ?>
                                        </span>
                                        <?php if ($quiz['time_limit']): ?>
                                            <span class="quiz-badge badge-time">
                                                <i class="fas fa-clock me-1"></i><?php echo $quiz['time_limit']; ?> دقيقة
                                            </span>
                                        <?php endif; ?>
                                        <span class="quiz-badge badge-questions">
                                            <i class="fas fa-list me-1"></i><?php echo $quiz['questions_count']; ?> سؤال
                                        </span>
                                    </div>
                                    
                                    <div class="quiz-stats">
                                        <div class="stat-item">
                                            <div class="stat-number"><?php echo $quiz['questions_count']; ?></div>
                                            <div class="stat-label">أسئلة</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-number"><?php echo $quiz['attempts_count']; ?></div>
                                            <div class="stat-label">محاولة</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-number"><?php echo $quiz['avg_score'] ? round($quiz['avg_score']) : 0; ?>%</div>
                                            <div class="stat-label">متوسط النتائج</div>
                                        </div>
                                    </div>
                                    
                                    <?php if ($userAttempt && $userAttempt['status'] == 'completed'): ?>
                                        <div class="score-display">
                                            <div class="score-number"><?php echo round($userAttempt['score']); ?>%</div>
                                            <div>نتيجتك في الاختبار</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($user): ?>
                                        <?php if ($canTakeQuiz): ?>
                                            <a href="take-quiz.php?id=<?php echo $quiz['id']; ?>" class="btn btn-primary w-100">
                                                <i class="fas fa-play me-2"></i>
                                                <?php echo $userAttempt ? 'إعادة الاختبار' : 'بدء الاختبار'; ?>
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-success w-100" disabled>
                                                <i class="fas fa-check me-2"></i>تم إكمال الاختبار
                                            </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <a href="login.php" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-sign-in-alt me-2"></i>سجل دخولك لبدء الاختبار
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-question-circle"></i>
                    <h3>لا توجد اختبارات متاحة</h3>
                    <p>لا توجد اختبارات متاحة لهذه المادة حالياً</p>
                    <a href="lessons.php?subject=<?php echo $subject['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للدروس
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات التحريك للبطاقات
            const quizCards = document.querySelectorAll('.quiz-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            quizCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
