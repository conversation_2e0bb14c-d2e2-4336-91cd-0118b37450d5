<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من معرف الصف
if (!isset($_GET['grade']) || !is_numeric($_GET['grade'])) {
    header('Location: grades.php');
    exit;
}

$gradeId = (int)$_GET['grade'];

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
    
    // التحقق من صلاحية الوصول للصف
    if (!canAccessGrade($user['id'], $gradeId)) {
        header('Location: grades.php');
        exit;
    }
}

// جلب بيانات الصف
$grade = fetchOne("SELECT * FROM grades WHERE id = :id", ['id' => $gradeId]);
if (!$grade) {
    header('Location: grades.php');
    exit;
}

// جلب المواد الدراسية للصف
$subjects = getSubjectsByGrade($gradeId);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($grade['name']); ?> - المواد الدراسية</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, <?php echo $grade['color']; ?> 0%, <?php echo $grade['color']; ?>CC 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .breadcrumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 0.75rem 1rem;
        }
        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: white;
        }
        .subject-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--subject-color, <?php echo $grade['color']; ?>);
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: var(--subject-color, <?php echo $grade['color']; ?>);
        }
        .subject-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
            background: var(--subject-color, <?php echo $grade['color']; ?>);
        }
        .subject-name {
            color: var(--dark-color);
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        .subject-description {
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }
        .subject-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--subject-color, <?php echo $grade['color']; ?>);
        }
        .stat-label {
            font-size: 0.8rem;
            color: var(--secondary-color);
        }
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--secondary-color);
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        .grade-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .grade-icon-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1.5rem;
            margin-left: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="grades.php">الصفوف الدراسية</a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($grade['name']); ?></li>
                </ol>
            </nav>
            
            <div class="grade-info">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="grade-icon-large">
                            <i class="fas <?php echo $grade['icon']; ?>"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h1 class="display-5 fw-bold mb-2"><?php echo htmlspecialchars($grade['name']); ?></h1>
                        <p class="lead mb-0">المواد الدراسية المتاحة</p>
                        <?php if ($grade['description']): ?>
                            <p class="mb-0 mt-2"><?php echo htmlspecialchars($grade['description']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <?php if (empty($subjects)): ?>
                <div class="empty-state">
                    <i class="fas fa-book"></i>
                    <h3>لا توجد مواد متاحة</h3>
                    <p>لا توجد مواد دراسية متاحة لهذا الصف حالياً</p>
                    <a href="grades.php" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للصفوف الدراسية
                    </a>
                </div>
            <?php else: ?>
                <div class="row g-4">
                    <?php foreach ($subjects as $subject): ?>
                        <?php
                        // جلب إحصائيات المادة
                        $lessonsCount = fetchOne("SELECT COUNT(*) as count FROM lessons WHERE subject_id = :subject_id AND status = 'published'", ['subject_id' => $subject['id']]);
                        $videosCount = fetchOne("SELECT COUNT(*) as count FROM lessons WHERE subject_id = :subject_id AND status = 'published' AND video_url IS NOT NULL", ['subject_id' => $subject['id']]);
                        $quizzesCount = fetchOne("SELECT COUNT(*) as count FROM quizzes WHERE subject_id = :subject_id AND status = 'published'", ['subject_id' => $subject['id']]);
                        ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="subject-card" style="--subject-color: <?php echo $subject['color']; ?>">
                                <div class="subject-icon">
                                    <i class="fas <?php echo $subject['icon']; ?>"></i>
                                </div>
                                
                                <h3 class="subject-name"><?php echo htmlspecialchars($subject['name']); ?></h3>
                                
                                <?php if ($subject['description']): ?>
                                    <p class="subject-description"><?php echo htmlspecialchars($subject['description']); ?></p>
                                <?php endif; ?>
                                
                                <div class="subject-stats">
                                    <div class="stat-item">
                                        <div class="stat-number"><?php echo $lessonsCount['count']; ?></div>
                                        <div class="stat-label">درس</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number"><?php echo $videosCount['count']; ?></div>
                                        <div class="stat-label">فيديو</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number"><?php echo $quizzesCount['count']; ?></div>
                                        <div class="stat-label">اختبار</div>
                                    </div>
                                </div>
                                
                                <a href="lessons.php?subject=<?php echo $subject['id']; ?>" 
                                   class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-arrow-left me-2"></i>استكشف الدروس
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التحريك
            const subjectCards = document.querySelectorAll('.subject-card');
            
            // مراقب التقاطع للتحريك عند الظهور
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            }, {
                threshold: 0.1
            });
            
            // تطبيق التأثيرات
            subjectCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
            
            // تحسين الروابط
            const subjectLinks = document.querySelectorAll('.subject-card a');
            subjectLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // إضافة تأثير التحميل
                    const button = this;
                    const originalText = button.innerHTML;
                    
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
                    button.disabled = true;
                    
                    // السماح للرابط بالعمل بعد تأخير قصير
                    setTimeout(() => {
                        window.location.href = this.href;
                    }, 500);
                    
                    e.preventDefault();
                });
            });
        });
    </script>
</body>
</html>
