<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] != 'admin') {
    header('Location: ../index.php');
    exit;
}

// معالجة الإجراءات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $userId = (int)$_POST['user_id'];
        
        switch ($action) {
            case 'activate':
                $result = updateRecord('users', ['status' => 'active'], 'id = :id', ['id' => $userId]);
                $message = $result ? 'تم تفعيل المستخدم بنجاح' : 'حدث خطأ أثناء تفعيل المستخدم';
                $messageType = $result ? 'success' : 'danger';
                break;
                
            case 'deactivate':
                $result = updateRecord('users', ['status' => 'inactive'], 'id = :id', ['id' => $userId]);
                $message = $result ? 'تم إلغاء تفعيل المستخدم بنجاح' : 'حدث خطأ أثناء إلغاء تفعيل المستخدم';
                $messageType = $result ? 'success' : 'danger';
                break;
                
            case 'delete':
                if ($userId != $user['id']) { // منع المدير من حذف نفسه
                    $result = deleteRecord('users', 'id = :id', ['id' => $userId]);
                    $message = $result ? 'تم حذف المستخدم بنجاح' : 'حدث خطأ أثناء حذف المستخدم';
                    $messageType = $result ? 'success' : 'danger';
                } else {
                    $message = 'لا يمكنك حذف حسابك الخاص';
                    $messageType = 'warning';
                }
                break;
        }
    }
}

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$roleFilter = isset($_GET['role']) ? sanitizeInput($_GET['role']) : '';
$statusFilter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(name LIKE :search OR email LIKE :search)";
    $params['search'] = "%$search%";
}

if (!empty($roleFilter)) {
    $whereConditions[] = "role = :role";
    $params['role'] = $roleFilter;
}

if (!empty($statusFilter)) {
    $whereConditions[] = "status = :status";
    $params['status'] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// جلب المستخدمين
$usersQuery = "
    SELECT u.*, g.name as grade_name 
    FROM users u 
    LEFT JOIN grades g ON u.grade_id = g.id 
    $whereClause 
    ORDER BY u.created_at DESC 
    LIMIT :limit OFFSET :offset
";

$stmt = $pdo->prepare($usersQuery);
foreach ($params as $key => $value) {
    $stmt->bindValue(":$key", $value);
}
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$users = $stmt->fetchAll();

// عدد المستخدمين الإجمالي
$countQuery = "SELECT COUNT(*) FROM users u $whereClause";
$stmt = $pdo->prepare($countQuery);
foreach ($params as $key => $value) {
    $stmt->bindValue(":$key", $value);
}
$stmt->execute();
$totalUsers = $stmt->fetchColumn();
$totalPages = ceil($totalUsers / $limit);

// إحصائيات سريعة
$stats = [
    'total' => fetchOne("SELECT COUNT(*) as count FROM users")['count'],
    'active' => fetchOne("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'],
    'students' => fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'student'")['count'],
    'teachers' => fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'teacher'")['count'],
    'parents' => fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'parent'")['count']
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة تحكم المدير</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
        }
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar-menu .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .main-content {
            margin-right: 250px;
            padding: 2rem;
            min-height: 100vh;
            background: #f8f9fa;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--card-color);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .role-student { background: #e3f2fd; color: #1976d2; }
        .role-teacher { background: #e8f5e8; color: #388e3c; }
        .role-parent { background: #fff3e0; color: #f57c00; }
        .role-admin { background: #fce4ec; color: #c2185b; }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .table-responsive {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .action-btn {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
            border-radius: 4px;
            border: none;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>لوحة التحكم</h4>
            <p class="mb-0">مرحباً <?php echo htmlspecialchars($user['name']); ?></p>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="users.php">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="grades.php">
                        <i class="fas fa-layer-group me-2"></i>إدارة الصفوف
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="subjects.php">
                        <i class="fas fa-book me-2"></i>إدارة المواد
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="lessons.php">
                        <i class="fas fa-chalkboard-teacher me-2"></i>إدارة الدروس
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>العودة للموقع
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">إدارة المستخدمين</h1>
            <a href="add-user.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
            </a>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="row g-3 mb-4">
            <div class="col-md-2">
                <div class="stats-card" style="--card-color: #007bff">
                    <h4 class="text-primary"><?php echo $stats['total']; ?></h4>
                    <small class="text-muted">إجمالي المستخدمين</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card" style="--card-color: #28a745">
                    <h4 class="text-success"><?php echo $stats['active']; ?></h4>
                    <small class="text-muted">نشط</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card" style="--card-color: #17a2b8">
                    <h4 class="text-info"><?php echo $stats['students']; ?></h4>
                    <small class="text-muted">طلاب</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card" style="--card-color: #ffc107">
                    <h4 class="text-warning"><?php echo $stats['teachers']; ?></h4>
                    <small class="text-muted">معلمين</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card" style="--card-color: #fd7e14">
                    <h4 style="color: #fd7e14"><?php echo $stats['parents']; ?></h4>
                    <small class="text-muted">أولياء أمور</small>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" placeholder="البحث بالاسم أو البريد الإلكتروني" 
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="role">
                            <option value="">جميع الأدوار</option>
                            <option value="student" <?php echo $roleFilter == 'student' ? 'selected' : ''; ?>>طالب</option>
                            <option value="teacher" <?php echo $roleFilter == 'teacher' ? 'selected' : ''; ?>>معلم</option>
                            <option value="parent" <?php echo $roleFilter == 'parent' ? 'selected' : ''; ?>>ولي أمر</option>
                            <option value="admin" <?php echo $roleFilter == 'admin' ? 'selected' : ''; ?>>مدير</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo $statusFilter == 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $statusFilter == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="users.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i>مسح
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>المستخدم</th>
                        <th>الدور</th>
                        <th>الصف</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>آخر دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد مستخدمين</p>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($users as $userData): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3">
                                            <?php echo strtoupper(substr($userData['name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($userData['name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($userData['email']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-<?php echo $userData['role']; ?>">
                                        <?php 
                                        $roles = ['student' => 'طالب', 'teacher' => 'معلم', 'parent' => 'ولي أمر', 'admin' => 'مدير'];
                                        echo $roles[$userData['role']];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo $userData['grade_name'] ? htmlspecialchars($userData['grade_name']) : '-'; ?>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $userData['status']; ?>">
                                        <?php echo $userData['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </td>
                                <td>
                                    <small><?php echo formatArabicDate($userData['created_at']); ?></small>
                                </td>
                                <td>
                                    <small><?php echo $userData['last_login'] ? formatArabicDate($userData['last_login']) : 'لم يسجل دخول'; ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="edit-user.php?id=<?php echo $userData['id']; ?>" 
                                           class="action-btn btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <?php if ($userData['status'] == 'active'): ?>
                                            <form method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                                <input type="hidden" name="action" value="deactivate">
                                                <input type="hidden" name="user_id" value="<?php echo $userData['id']; ?>">
                                                <button type="submit" class="action-btn btn btn-outline-warning" title="إلغاء التفعيل">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="action" value="activate">
                                                <input type="hidden" name="user_id" value="<?php echo $userData['id']; ?>">
                                                <button type="submit" class="action-btn btn btn-outline-success" title="تفعيل">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <?php if ($userData['id'] != $user['id']): ?>
                                            <form method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="user_id" value="<?php echo $userData['id']; ?>">
                                                <button type="submit" class="action-btn btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>&status=<?php echo urlencode($statusFilter); ?>">السابق</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page-2); $i <= min($totalPages, $page+2); $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>&status=<?php echo urlencode($statusFilter); ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($roleFilter); ?>&status=<?php echo urlencode($statusFilter); ?>">التالي</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
