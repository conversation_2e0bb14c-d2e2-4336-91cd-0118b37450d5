<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من معرف الملف
if (!isset($_GET['file']) || !is_numeric($_GET['file'])) {
    header('HTTP/1.0 404 Not Found');
    die('الملف غير موجود');
}

$fileId = (int)$_GET['file'];

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// جلب بيانات الملف
$file = fetchOne("
    SELECT lf.*, l.title as lesson_title, s.grade_id 
    FROM lesson_files lf 
    JOIN lessons l ON lf.lesson_id = l.id 
    JOIN subjects s ON l.subject_id = s.id 
    WHERE lf.id = :id
", ['id' => $fileId]);

if (!$file) {
    header('HTTP/1.0 404 Not Found');
    die('الملف غير موجود');
}

// التحقق من صلاحية الوصول
if ($user && !canAccessGrade($user['id'], $file['grade_id'])) {
    header('HTTP/1.0 403 Forbidden');
    die('غير مصرح لك بتحميل هذا الملف');
}

// التحقق من وجود الملف فعلياً
$filePath = $file['file_path'];
if (!file_exists($filePath)) {
    header('HTTP/1.0 404 Not Found');
    die('الملف غير موجود على الخادم');
}

try {
    // تحديث عداد التحميل
    updateRecord('lesson_files', 
        ['download_count' => $file['download_count'] + 1], 
        'id = :id', 
        ['id' => $fileId]
    );
    
    // تحديد نوع المحتوى
    $mimeType = mime_content_type($filePath);
    if (!$mimeType) {
        $mimeType = 'application/octet-stream';
    }
    
    // إعداد headers للتحميل
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $file['original_name'] . '"');
    header('Content-Length: ' . filesize($filePath));
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // منع التخزين المؤقت
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    
    // قراءة وإرسال الملف
    $handle = fopen($filePath, 'rb');
    if ($handle) {
        while (!feof($handle)) {
            echo fread($handle, 8192);
            flush();
        }
        fclose($handle);
    } else {
        header('HTTP/1.0 500 Internal Server Error');
        die('خطأ في قراءة الملف');
    }
    
} catch (Exception $e) {
    error_log("Download error: " . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    die('حدث خطأ أثناء تحميل الملف');
}
?>
