<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] != 'admin') {
    header('Location: ../index.php');
    exit;
}

// جلب الإحصائيات
$stats = [
    'total_users' => fetchOne("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'],
    'total_students' => fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'student' AND status = 'active'")['count'],
    'total_teachers' => fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'teacher' AND status = 'active'")['count'],
    'total_parents' => fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'parent' AND status = 'active'")['count'],
    'total_grades' => fetchOne("SELECT COUNT(*) as count FROM grades WHERE status = 'active'")['count'],
    'total_subjects' => fetchOne("SELECT COUNT(*) as count FROM subjects WHERE status = 'active'")['count'],
    'total_lessons' => fetchOne("SELECT COUNT(*) as count FROM lessons WHERE status = 'published'")['count'],
    'total_quizzes' => fetchOne("SELECT COUNT(*) as count FROM quizzes WHERE status = 'published'")['count']
];

// جلب آخر المستخدمين المسجلين
$recent_users = fetchAll("SELECT id, name, email, role, created_at FROM users WHERE status = 'active' ORDER BY created_at DESC LIMIT 5");

// جلب آخر الدروس المضافة
$recent_lessons = fetchAll("
    SELECT l.id, l.title, l.created_at, s.name as subject_name, g.name as grade_name 
    FROM lessons l 
    JOIN subjects s ON l.subject_id = s.id 
    JOIN grades g ON s.grade_id = g.id 
    WHERE l.status = 'published' 
    ORDER BY l.created_at DESC 
    LIMIT 5
");

// جلب الدروس الأكثر مشاهدة
$popular_lessons = fetchAll("
    SELECT l.id, l.title, l.views_count, s.name as subject_name, g.name as grade_name 
    FROM lessons l 
    JOIN subjects s ON l.subject_id = s.id 
    JOIN grades g ON s.grade_id = g.id 
    WHERE l.status = 'published' 
    ORDER BY l.views_count DESC 
    LIMIT 5
");
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar-menu {
            padding: 1rem 0;
        }
        .sidebar-menu .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .main-content {
            margin-right: 250px;
            padding: 2rem;
            min-height: 100vh;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--card-color, #007bff);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--card-color, #007bff);
        }
        .stat-label {
            color: #6c757d;
            font-weight: 600;
        }
        .stat-icon {
            font-size: 2rem;
            color: var(--card-color, #007bff);
            opacity: 0.7;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .content-card-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        .content-card-body {
            padding: 1.5rem;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .role-student { background: #e3f2fd; color: #1976d2; }
        .role-teacher { background: #e8f5e8; color: #388e3c; }
        .role-parent { background: #fff3e0; color: #f57c00; }
        .role-admin { background: #fce4ec; color: #c2185b; }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-graduation-cap me-2"></i>لوحة التحكم</h4>
            <p class="mb-0">مرحباً <?php echo htmlspecialchars($user['name']); ?></p>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="grades.php">
                        <i class="fas fa-layer-group me-2"></i>إدارة الصفوف
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="subjects.php">
                        <i class="fas fa-book me-2"></i>إدارة المواد
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="lessons.php">
                        <i class="fas fa-chalkboard-teacher me-2"></i>إدارة الدروس
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="quizzes.php">
                        <i class="fas fa-question-circle me-2"></i>إدارة الاختبارات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="settings.php">
                        <i class="fas fa-cog me-2"></i>الإعدادات
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>العودة للموقع
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">لوحة التحكم الرئيسية</h1>
            <button class="btn btn-primary d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #007bff">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                            <div class="stat-label">إجمالي المستخدمين</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #28a745">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $stats['total_students']; ?></div>
                            <div class="stat-label">الطلاب</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #17a2b8">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $stats['total_lessons']; ?></div>
                            <div class="stat-label">الدروس</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #ffc107">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $stats['total_quizzes']; ?></div>
                            <div class="stat-label">الاختبارات</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Cards -->
        <div class="row g-4">
            <!-- Recent Users -->
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="content-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>آخر المستخدمين المسجلين
                        </h5>
                    </div>
                    <div class="content-card-body">
                        <?php if (empty($recent_users)): ?>
                            <p class="text-muted text-center">لا توجد بيانات</p>
                        <?php else: ?>
                            <?php foreach ($recent_users as $recent_user): ?>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="user-avatar me-3">
                                        <?php echo strtoupper(substr($recent_user['name'], 0, 1)); ?>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($recent_user['name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($recent_user['email']); ?></small>
                                    </div>
                                    <div>
                                        <span class="role-badge role-<?php echo $recent_user['role']; ?>">
                                            <?php 
                                            $roles = ['student' => 'طالب', 'teacher' => 'معلم', 'parent' => 'ولي أمر', 'admin' => 'مدير'];
                                            echo $roles[$recent_user['role']];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Lessons -->
            <div class="col-lg-6">
                <div class="content-card">
                    <div class="content-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book-open me-2"></i>آخر الدروس المضافة
                        </h5>
                    </div>
                    <div class="content-card-body">
                        <?php if (empty($recent_lessons)): ?>
                            <p class="text-muted text-center">لا توجد دروس</p>
                        <?php else: ?>
                            <?php foreach ($recent_lessons as $lesson): ?>
                                <div class="mb-3">
                                    <h6 class="mb-1">
                                        <a href="../lesson.php?id=<?php echo $lesson['id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($lesson['title']); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($lesson['grade_name']); ?> - 
                                        <?php echo htmlspecialchars($lesson['subject_name']); ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التحريك للبطاقات
            const cards = document.querySelectorAll('.stat-card, .content-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
