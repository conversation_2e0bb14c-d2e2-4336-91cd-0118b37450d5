<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    header('Location: login.php');
    exit;
}

// جلب إحصائيات المستخدم
$stats = getUserStats($user['id']);

// جلب الدروس المفضلة
$favoriteQuery = "
    SELECT l.*, s.name as subject_name, g.name as grade_name, s.color as subject_color
    FROM favorites f
    JOIN lessons l ON f.lesson_id = l.id
    JOIN subjects s ON l.subject_id = s.id
    JOIN grades g ON s.grade_id = g.id
    WHERE f.user_id = :user_id AND l.status = 'published'
    ORDER BY f.created_at DESC
    LIMIT 6
";
$favoriteLessons = fetchAll($favoriteQuery, ['user_id' => $user['id']]);

// جلب الدروس قيد التقدم
$progressQuery = "
    SELECT l.*, s.name as subject_name, g.name as grade_name, s.color as subject_color, lp.progress, lp.updated_at as last_accessed
    FROM lesson_progress lp
    JOIN lessons l ON lp.lesson_id = l.id
    JOIN subjects s ON l.subject_id = s.id
    JOIN grades g ON s.grade_id = g.id
    WHERE lp.user_id = :user_id AND lp.progress > 0 AND lp.progress < 100 AND l.status = 'published'
    ORDER BY lp.updated_at DESC
    LIMIT 6
";
$progressLessons = fetchAll($progressQuery, ['user_id' => $user['id']]);

// جلب الدروس المكتملة حديثاً
$completedQuery = "
    SELECT l.*, s.name as subject_name, g.name as grade_name, s.color as subject_color, lp.completed_at
    FROM lesson_progress lp
    JOIN lessons l ON lp.lesson_id = l.id
    JOIN subjects s ON l.subject_id = s.id
    JOIN grades g ON s.grade_id = g.id
    WHERE lp.user_id = :user_id AND lp.progress >= 100 AND l.status = 'published'
    ORDER BY lp.completed_at DESC
    LIMIT 6
";
$completedLessons = fetchAll($completedQuery, ['user_id' => $user['id']]);

// جلب المواد المتاحة للطالب
if ($user['role'] == 'student' && $user['grade_id']) {
    $availableSubjects = getSubjectsByGrade($user['grade_id']);
} else {
    $availableSubjects = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دروسي - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid var(--card-color, #007bff);
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--card-color, #007bff);
            margin-bottom: 0.5rem;
        }
        .stats-label {
            color: #6c757d;
            font-weight: 600;
        }
        .lesson-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            overflow: hidden;
            height: 100%;
        }
        .lesson-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: var(--subject-color, #007bff);
        }
        .lesson-thumbnail {
            height: 150px;
            background: linear-gradient(135deg, var(--subject-color, #007bff)20 0%, var(--subject-color, #007bff)40 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .lesson-content {
            padding: 1.5rem;
        }
        .lesson-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }
        .lesson-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.8rem;
            color: var(--secondary-color);
        }
        .progress-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: conic-gradient(var(--subject-color, #007bff) var(--progress, 0%), #e9ecef 0%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .progress-circle::before {
            content: '';
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--subject-color, #007bff);
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: var(--secondary-color);
        }
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        .subject-quick-access {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .subject-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
        }
        .subject-item:hover {
            background: #e9ecef;
            color: inherit;
        }
        .subject-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--subject-color, #007bff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="my-courses.php">
                            <i class="fas fa-book-open me-1"></i>دروسي
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($user['name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">
                                <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                            </a></li>
                            <?php if ($user['role'] == 'admin'): ?>
                                <li><a class="dropdown-item" href="admin/dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a></li>
                            <?php elseif ($user['role'] == 'parent'): ?>
                                <li><a class="dropdown-item" href="parent/dashboard.php">
                                    <i class="fas fa-child me-2"></i>متابعة الأبناء
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-3">مرحباً <?php echo htmlspecialchars($user['name']); ?></h1>
                    <p class="lead mb-0">تابع تقدمك الدراسي واستكمل دروسك</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex gap-2 justify-content-md-end">
                        <a href="grades.php" class="btn btn-outline-light">
                            <i class="fas fa-search me-1"></i>استكشف المزيد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <!-- Statistics -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="--card-color: #007bff">
                        <div class="stats-number"><?php echo $stats['completed_lessons']; ?></div>
                        <div class="stats-label">دروس مكتملة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="--card-color: #28a745">
                        <div class="stats-number"><?php echo $stats['in_progress_lessons']; ?></div>
                        <div class="stats-label">دروس قيد التقدم</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="--card-color: #ffc107">
                        <div class="stats-number"><?php echo count($favoriteLessons); ?></div>
                        <div class="stats-label">دروس مفضلة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="--card-color: #17a2b8">
                        <div class="stats-number"><?php echo round($stats['avg_progress']); ?>%</div>
                        <div class="stats-label">متوسط التقدم</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Continue Learning -->
                    <?php if (!empty($progressLessons)): ?>
                        <div class="mb-5">
                            <h2 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                واصل التعلم
                            </h2>
                            
                            <div class="row g-4">
                                <?php foreach ($progressLessons as $lesson): ?>
                                    <div class="col-md-6">
                                        <div class="lesson-card" style="--subject-color: <?php echo $lesson['subject_color']; ?>">
                                            <div class="lesson-thumbnail">
                                                <i class="fas fa-chalkboard-teacher" style="font-size: 2rem; color: white; opacity: 0.7;"></i>
                                            </div>
                                            <div class="lesson-content">
                                                <h3 class="lesson-title"><?php echo htmlspecialchars($lesson['title']); ?></h3>
                                                <div class="lesson-meta">
                                                    <span><?php echo htmlspecialchars($lesson['subject_name']); ?></span>
                                                    <span>آخر وصول: <?php echo formatArabicDate($lesson['last_accessed']); ?></span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <a href="lesson.php?id=<?php echo $lesson['id']; ?>" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-play me-1"></i>متابعة
                                                    </a>
                                                    <div class="progress-circle" style="--progress: <?php echo $lesson['progress']; ?>%">
                                                        <div class="progress-text"><?php echo round($lesson['progress']); ?>%</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Favorite Lessons -->
                    <?php if (!empty($favoriteLessons)): ?>
                        <div class="mb-5">
                            <h2 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-heart"></i>
                                </div>
                                الدروس المفضلة
                            </h2>
                            
                            <div class="row g-4">
                                <?php foreach ($favoriteLessons as $lesson): ?>
                                    <div class="col-md-6">
                                        <div class="lesson-card" style="--subject-color: <?php echo $lesson['subject_color']; ?>">
                                            <div class="lesson-thumbnail">
                                                <i class="fas fa-heart" style="font-size: 2rem; color: white; opacity: 0.7;"></i>
                                            </div>
                                            <div class="lesson-content">
                                                <h3 class="lesson-title"><?php echo htmlspecialchars($lesson['title']); ?></h3>
                                                <div class="lesson-meta">
                                                    <span><?php echo htmlspecialchars($lesson['subject_name']); ?></span>
                                                    <span><?php echo htmlspecialchars($lesson['grade_name']); ?></span>
                                                </div>
                                                <a href="lesson.php?id=<?php echo $lesson['id']; ?>" class="btn btn-outline-primary btn-sm w-100">
                                                    <i class="fas fa-eye me-1"></i>مشاهدة الدرس
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Completed Lessons -->
                    <?php if (!empty($completedLessons)): ?>
                        <div class="mb-5">
                            <h2 class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-check"></i>
                                </div>
                                الدروس المكتملة حديثاً
                            </h2>
                            
                            <div class="row g-4">
                                <?php foreach ($completedLessons as $lesson): ?>
                                    <div class="col-md-6">
                                        <div class="lesson-card" style="--subject-color: <?php echo $lesson['subject_color']; ?>">
                                            <div class="lesson-thumbnail">
                                                <i class="fas fa-check-circle" style="font-size: 2rem; color: white; opacity: 0.7;"></i>
                                            </div>
                                            <div class="lesson-content">
                                                <h3 class="lesson-title"><?php echo htmlspecialchars($lesson['title']); ?></h3>
                                                <div class="lesson-meta">
                                                    <span><?php echo htmlspecialchars($lesson['subject_name']); ?></span>
                                                    <span>مكتمل: <?php echo formatArabicDate($lesson['completed_at']); ?></span>
                                                </div>
                                                <a href="lesson.php?id=<?php echo $lesson['id']; ?>" class="btn btn-success btn-sm w-100">
                                                    <i class="fas fa-redo me-1"></i>مراجعة الدرس
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Empty State -->
                    <?php if (empty($progressLessons) && empty($favoriteLessons) && empty($completedLessons)): ?>
                        <div class="empty-state">
                            <i class="fas fa-book-open"></i>
                            <h3>ابدأ رحلتك التعليمية</h3>
                            <p>لم تبدأ أي دروس بعد. استكشف المواد المتاحة وابدأ التعلم!</p>
                            <a href="grades.php" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>استكشف الدروس
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Access to Subjects -->
                    <?php if (!empty($availableSubjects)): ?>
                        <div class="subject-quick-access">
                            <h5 class="mb-3">
                                <i class="fas fa-bolt me-2"></i>الوصول السريع للمواد
                            </h5>
                            <?php foreach ($availableSubjects as $subject): ?>
                                <a href="lessons.php?subject=<?php echo $subject['id']; ?>" 
                                   class="subject-item" 
                                   style="--subject-color: <?php echo $subject['color']; ?>">
                                    <div class="subject-icon">
                                        <i class="fas <?php echo $subject['icon']; ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold"><?php echo htmlspecialchars($subject['name']); ?></div>
                                        <?php 
                                        $subjectLessons = fetchOne("SELECT COUNT(*) as count FROM lessons WHERE subject_id = :id AND status = 'published'", ['id' => $subject['id']]);
                                        ?>
                                        <small class="text-muted"><?php echo $subjectLessons['count']; ?> درس</small>
                                    </div>
                                    <i class="fas fa-arrow-left"></i>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات التحريك
            const cards = document.querySelectorAll('.stats-card, .lesson-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 50);
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
