<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من معرف الدرس
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: grades.php');
    exit;
}

$lessonId = (int)$_GET['id'];

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// جلب بيانات الدرس
$lesson = getLessonById($lessonId);
if (!$lesson) {
    header('Location: grades.php');
    exit;
}

// التحقق من صلاحية الوصول
if ($user) {
    $subjectData = fetchOne("SELECT grade_id FROM subjects WHERE id = :id", ['id' => $lesson['subject_id']]);
    if (!canAccessGrade($user['id'], $subjectData['grade_id'])) {
        header('Location: grades.php');
        exit;
    }
}

// تحديث عدد المشاهدات
updateRecord('lessons', ['views_count' => $lesson['views_count'] + 1], 'id = :id', ['id' => $lessonId]);

// جلب ملفات الدرس
$lessonFiles = fetchAll("SELECT * FROM lesson_files WHERE lesson_id = :lesson_id ORDER BY id ASC", ['lesson_id' => $lessonId]);

// جلب تقدم الطالب
$userProgress = null;
if ($user && $user['role'] == 'student') {
    $userProgress = getLessonProgress($user['id'], $lessonId);
}

// جلب التعليقات
$comments = fetchAll("
    SELECT c.*, u.name as user_name 
    FROM comments c 
    JOIN users u ON c.user_id = u.id 
    WHERE c.lesson_id = :lesson_id AND c.status = 'approved' AND c.parent_id IS NULL
    ORDER BY c.created_at DESC
", ['lesson_id' => $lessonId]);

// معالجة إضافة تعليق
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_comment']) && $user) {
    $content = sanitizeInput($_POST['content']);
    if (!empty($content)) {
        $commentData = [
            'lesson_id' => $lessonId,
            'user_id' => $user['id'],
            'content' => $content,
            'status' => 'approved' // يمكن تغييرها لـ 'pending' للمراجعة
        ];
        insertRecord('comments', $commentData);
        header("Location: lesson.php?id=$lessonId#comments");
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($lesson['title']); ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .lesson-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-top: 76px;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 2rem;
        }
        .video-container iframe,
        .video-container video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .lesson-content {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .lesson-sidebar {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: sticky;
            top: 100px;
        }
        .lesson-meta {
            display: flex;
            gap: 2rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }
        .lesson-objectives {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }
        .file-item:hover {
            background: #e9ecef;
        }
        .file-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        .file-icon.pdf { background: #dc3545; }
        .file-icon.doc { background: #0d6efd; }
        .file-icon.image { background: #198754; }
        .file-icon.video { background: #6f42c1; }
        .file-icon.audio { background: #fd7e14; }
        .file-icon.default { background: #6c757d; }
        .progress-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .comment-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .comment-author {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        .comment-date {
            font-size: 0.8rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }
        .comment-content {
            line-height: 1.6;
        }
        .btn-favorite {
            transition: all 0.3s ease;
        }
        .btn-favorite.active {
            background: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        .lesson-navigation {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .nav-lesson {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }
        .nav-lesson:hover {
            background: #e9ecef;
            color: inherit;
        }
        .nav-lesson-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Lesson Header -->
    <section class="lesson-header">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php" class="text-white-50">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="grades.php" class="text-white-50">الصفوف الدراسية</a></li>
                    <li class="breadcrumb-item"><a href="lessons.php?subject=<?php echo $lesson['subject_id']; ?>" class="text-white-50"><?php echo htmlspecialchars($lesson['subject_name']); ?></a></li>
                    <li class="breadcrumb-item active text-white"><?php echo htmlspecialchars($lesson['title']); ?></li>
                </ol>
            </nav>
            
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-6 fw-bold mb-3"><?php echo htmlspecialchars($lesson['title']); ?></h1>
                    <div class="lesson-meta">
                        <div class="meta-item">
                            <i class="fas fa-layer-group"></i>
                            <span><?php echo htmlspecialchars($lesson['grade_name']); ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-book"></i>
                            <span><?php echo htmlspecialchars($lesson['subject_name']); ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span><?php echo $lesson['estimated_time'] ? $lesson['estimated_time'] . ' دقيقة' : 'غير محدد'; ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span><?php echo $lesson['views_count']; ?> مشاهدة</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <?php if ($user): ?>
                        <button class="btn btn-outline-light btn-favorite me-2" data-lesson-id="<?php echo $lesson['id']; ?>">
                            <i class="far fa-heart me-1"></i>إضافة للمفضلة
                        </button>
                        <button class="btn btn-outline-light btn-share" data-url="<?php echo "http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]"; ?>" data-title="<?php echo htmlspecialchars($lesson['title']); ?>">
                            <i class="fas fa-share me-1"></i>مشاركة
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Video Section -->
                    <?php if ($lesson['video_url']): ?>
                        <div class="video-container">
                            <?php if (strpos($lesson['video_url'], 'youtube.com') !== false || strpos($lesson['video_url'], 'youtu.be') !== false): ?>
                                <?php
                                // تحويل رابط YouTube إلى embed
                                $videoId = '';
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $lesson['video_url'], $matches)) {
                                    $videoId = $matches[1];
                                }
                                ?>
                                <iframe src="https://www.youtube.com/embed/<?php echo $videoId; ?>?rel=0" 
                                        frameborder="0" 
                                        allowfullscreen></iframe>
                            <?php else: ?>
                                <video controls>
                                    <source src="<?php echo htmlspecialchars($lesson['video_url']); ?>" type="video/mp4">
                                    متصفحك لا يدعم تشغيل الفيديو.
                                </video>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Progress Section for Students -->
                    <?php if ($user && $user['role'] == 'student'): ?>
                        <div class="progress-section">
                            <h5><i class="fas fa-chart-line me-2"></i>تقدمك في هذا الدرس</h5>
                            <div class="progress mb-2" style="height: 10px;">
                                <div class="progress-bar" style="width: <?php echo $userProgress ? $userProgress['progress'] : 0; ?>%"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small>التقدم: <?php echo $userProgress ? round($userProgress['progress']) : 0; ?>%</small>
                                <button class="btn btn-sm btn-success" onclick="markAsComplete()">
                                    <i class="fas fa-check me-1"></i>تم الانتهاء
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Lesson Objectives -->
                    <?php if ($lesson['objectives']): ?>
                        <div class="lesson-objectives">
                            <h5><i class="fas fa-bullseye me-2"></i>أهداف الدرس</h5>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($lesson['objectives'])); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Lesson Content -->
                    <div class="lesson-content">
                        <h5><i class="fas fa-book-open me-2"></i>محتوى الدرس</h5>
                        <?php if ($lesson['content']): ?>
                            <div class="lesson-text">
                                <?php echo $lesson['content']; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">لا يوجد محتوى نصي لهذا الدرس.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Comments Section -->
                    <div class="lesson-content" id="comments">
                        <h5><i class="fas fa-comments me-2"></i>التعليقات</h5>
                        
                        <?php if ($user): ?>
                            <form method="POST" class="mb-4">
                                <div class="mb-3">
                                    <textarea class="form-control" name="content" rows="3" placeholder="اكتب تعليقك هنا..." required></textarea>
                                </div>
                                <button type="submit" name="add_comment" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-1"></i>إضافة تعليق
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if (empty($comments)): ?>
                            <p class="text-muted">لا توجد تعليقات بعد. كن أول من يعلق!</p>
                        <?php else: ?>
                            <?php foreach ($comments as $comment): ?>
                                <div class="comment-item">
                                    <div class="comment-author"><?php echo htmlspecialchars($comment['user_name']); ?></div>
                                    <div class="comment-date"><?php echo formatArabicDate($comment['created_at']); ?></div>
                                    <div class="comment-content"><?php echo nl2br(htmlspecialchars($comment['content'])); ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="lesson-sidebar">
                        <!-- Files Section -->
                        <?php if (!empty($lessonFiles)): ?>
                            <h6><i class="fas fa-download me-2"></i>ملفات الدرس</h6>
                            <?php foreach ($lessonFiles as $file): ?>
                                <div class="file-item">
                                    <div class="file-info">
                                        <div class="file-icon <?php echo getFileType($file['filename']); ?>">
                                            <i class="fas fa-file"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?php echo htmlspecialchars($file['original_name']); ?></div>
                                            <small class="text-muted"><?php echo formatFileSize($file['file_size']); ?></small>
                                        </div>
                                    </div>
                                    <a href="download.php?file=<?php echo $file['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                            <hr>
                        <?php endif; ?>

                        <!-- Lesson Info -->
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الدرس</h6>
                        <div class="mb-3">
                            <small class="text-muted d-block">مستوى الصعوبة</small>
                            <span class="badge bg-<?php echo $lesson['difficulty'] == 'easy' ? 'success' : ($lesson['difficulty'] == 'medium' ? 'warning' : 'danger'); ?>">
                                <?php 
                                $difficulties = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                echo $difficulties[$lesson['difficulty']];
                                ?>
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted d-block">تاريخ الإضافة</small>
                            <span><?php echo formatArabicDate($lesson['created_at']); ?></span>
                        </div>

                        <?php if ($lesson['keywords']): ?>
                            <div class="mb-3">
                                <small class="text-muted d-block">الكلمات المفتاحية</small>
                                <span><?php echo htmlspecialchars($lesson['keywords']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // تسجيل إكمال الدرس
        function markAsComplete() {
            <?php if ($user && $user['role'] == 'student'): ?>
                fetch('api/update-progress.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        lesson_id: <?php echo $lessonId; ?>,
                        progress: 100
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.querySelector('.progress-bar').style.width = '100%';
                        EducationalPlatform.showAlert('success', 'تم تسجيل إكمال الدرس بنجاح!');
                    }
                });
            <?php else: ?>
                EducationalPlatform.showAlert('warning', 'يجب تسجيل الدخول كطالب لحفظ التقدم');
            <?php endif; ?>
        }

        // تحديث التقدم أثناء مشاهدة الفيديو
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('video');
            if (video) {
                video.addEventListener('timeupdate', function() {
                    const progress = (video.currentTime / video.duration) * 100;
                    if (progress > 0 && <?php echo $user && $user['role'] == 'student' ? 'true' : 'false'; ?>) {
                        // تحديث التقدم كل 10 ثوان
                        if (Math.floor(video.currentTime) % 10 === 0) {
                            updateProgress(Math.round(progress));
                        }
                    }
                });
            }
        });

        function updateProgress(progress) {
            fetch('api/update-progress.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    lesson_id: <?php echo $lessonId; ?>,
                    progress: progress
                })
            });
        }
    </script>
</body>
</html>
