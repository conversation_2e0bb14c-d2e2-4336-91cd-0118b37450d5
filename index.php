<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة التعليم التفاعلي - الصفحة الرئيسية</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold text-white mb-4">
                            مرحباً بك في منصة التعليم التفاعلي
                        </h1>
                        <p class="lead text-white-50 mb-4">
                            منصة تعليمية شاملة تخدم جميع المراحل الدراسية من الصف الأول ابتدائي حتى الثالث ثانوي
                            بأحدث الطرق التفاعلية والتقنيات التعليمية المتطورة
                        </p>
                        <div class="hero-buttons">
                            <?php if (!$user): ?>
                                <a href="register.php" class="btn btn-light btn-lg me-3">
                                    <i class="fas fa-rocket me-2"></i>ابدأ رحلتك التعليمية
                                </a>
                                <a href="grades.php" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-eye me-2"></i>استكشف المحتوى
                                </a>
                            <?php else: ?>
                                <a href="grades.php" class="btn btn-light btn-lg me-3">
                                    <i class="fas fa-book-open me-2"></i>تصفح الدروس
                                </a>
                                <a href="my-courses.php" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-graduation-cap me-2"></i>دروسي
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image text-center">
                        <i class="fas fa-graduation-cap hero-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">لماذا تختار منصتنا؟</h2>
                    <p class="section-subtitle">نقدم تجربة تعليمية متميزة بأحدث التقنيات</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h4>محتوى شامل</h4>
                        <p>جميع المراحل الدراسية من الابتدائي حتى الثانوي مع مناهج محدثة</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h4>فيديوهات تفاعلية</h4>
                        <p>شروحات مرئية عالية الجودة مع إمكانية التفاعل والمشاركة</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>تتبع التقدم</h4>
                        <p>نظام متقدم لمتابعة الأداء والتقدم الدراسي مع تقارير مفصلة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Grades Preview Section -->
    <section class="grades-preview py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="section-title">المراحل الدراسية</h2>
                    <p class="section-subtitle">اختر المرحلة المناسبة لك</p>
                </div>
            </div>
            
            <div class="row g-4">
                <?php
                $grades = [
                    ['id' => 1, 'name' => 'الأول ابتدائي', 'color' => 'primary', 'icon' => 'fa-baby'],
                    ['id' => 2, 'name' => 'الثاني ابتدائي', 'color' => 'success', 'icon' => 'fa-child'],
                    ['id' => 3, 'name' => 'الثالث ابتدائي', 'color' => 'info', 'icon' => 'fa-user-graduate'],
                    ['id' => 4, 'name' => 'الرابع ابتدائي', 'color' => 'warning', 'icon' => 'fa-book'],
                    ['id' => 5, 'name' => 'الخامس ابتدائي', 'color' => 'danger', 'icon' => 'fa-pencil-alt'],
                    ['id' => 6, 'name' => 'السادس ابتدائي', 'color' => 'secondary', 'icon' => 'fa-graduation-cap']
                ];
                
                foreach (array_slice($grades, 0, 6) as $grade): ?>
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="grade-card">
                            <a href="subjects.php?grade=<?php echo $grade['id']; ?>" class="text-decoration-none">
                                <div class="grade-icon bg-<?php echo $grade['color']; ?>">
                                    <i class="fas <?php echo $grade['icon']; ?>"></i>
                                </div>
                                <h5 class="grade-name"><?php echo $grade['name']; ?></h5>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="grades.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-eye me-2"></i>عرض جميع المراحل
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>
