<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول وصلاحيات ولي الأمر
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user || $user['role'] != 'parent') {
    header('Location: ../index.php');
    exit;
}

// جلب أبناء ولي الأمر
$children = fetchAll("
    SELECT u.*, g.name as grade_name, g.color as grade_color
    FROM parent_students ps
    JOIN users u ON ps.student_id = u.id
    JOIN grades g ON u.grade_id = g.id
    WHERE ps.parent_id = :parent_id AND u.status = 'active'
    ORDER BY g.level ASC, g.order_num ASC, u.name ASC
", ['parent_id' => $user['id']]);

// جلب إحصائيات شاملة لجميع الأبناء
$totalStats = [
    'total_children' => count($children),
    'total_completed_lessons' => 0,
    'total_in_progress_lessons' => 0,
    'avg_progress' => 0
];

$childrenStats = [];
foreach ($children as $child) {
    $childStats = getUserStats($child['id']);
    $childrenStats[$child['id']] = $childStats;
    
    $totalStats['total_completed_lessons'] += $childStats['completed_lessons'];
    $totalStats['total_in_progress_lessons'] += $childStats['in_progress_lessons'];
    $totalStats['avg_progress'] += $childStats['avg_progress'];
}

if (count($children) > 0) {
    $totalStats['avg_progress'] = round($totalStats['avg_progress'] / count($children), 2);
}

// جلب آخر الأنشطة للأبناء
$recentActivities = [];
if (!empty($children)) {
    $childrenIds = array_column($children, 'id');
    $placeholders = str_repeat('?,', count($childrenIds) - 1) . '?';
    
    $recentActivities = fetchAll("
        SELECT lp.*, l.title as lesson_title, s.name as subject_name, g.name as grade_name, u.name as student_name
        FROM lesson_progress lp
        JOIN lessons l ON lp.lesson_id = l.id
        JOIN subjects s ON l.subject_id = s.id
        JOIN grades g ON s.grade_id = g.id
        JOIN users u ON lp.user_id = u.id
        WHERE lp.user_id IN ($placeholders)
        ORDER BY lp.updated_at DESC
        LIMIT 10
    ", $childrenIds);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم ولي الأمر - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .sidebar-menu {
            padding: 1rem 0;
        }
        .sidebar-menu .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        .sidebar-menu .nav-link:hover,
        .sidebar-menu .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .main-content {
            margin-right: 250px;
            padding: 2rem;
            min-height: 100vh;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid var(--card-color, #007bff);
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--card-color, #007bff);
        }
        .stat-label {
            color: #6c757d;
            font-weight: 600;
        }
        .child-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid var(--grade-color, #007bff);
            margin-bottom: 1.5rem;
        }
        .child-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .child-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--grade-color, #007bff) 0%, var(--grade-color, #007bff)CC 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }
        .progress-ring {
            width: 80px;
            height: 80px;
            position: relative;
        }
        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
        }
        .progress-ring .bg {
            stroke: #e9ecef;
        }
        .progress-ring .progress {
            stroke: var(--grade-color, #007bff);
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--grade-color, #007bff);
        }
        .activity-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 3px solid var(--activity-color, #007bff);
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--activity-color, #007bff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
        }
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .content-card-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        .content-card-body {
            padding: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-child me-2"></i>متابعة الأبناء</h4>
            <p class="mb-0">مرحباً <?php echo htmlspecialchars($user['name']); ?></p>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="children.php">
                        <i class="fas fa-users me-2"></i>إدارة الأبناء
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="progress.php">
                        <i class="fas fa-chart-line me-2"></i>تقارير التقدم
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="messages.php">
                        <i class="fas fa-envelope me-2"></i>الرسائل
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="notifications.php">
                        <i class="fas fa-bell me-2"></i>الإشعارات
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i>العودة للموقع
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="../logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">لوحة تحكم ولي الأمر</h1>
            <button class="btn btn-primary d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #007bff">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $totalStats['total_children']; ?></div>
                            <div class="stat-label">عدد الأبناء</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users" style="font-size: 2rem; color: #007bff; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #28a745">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $totalStats['total_completed_lessons']; ?></div>
                            <div class="stat-label">دروس مكتملة</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle" style="font-size: 2rem; color: #28a745; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #ffc107">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $totalStats['total_in_progress_lessons']; ?></div>
                            <div class="stat-label">دروس قيد التقدم</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-clock" style="font-size: 2rem; color: #ffc107; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="--card-color: #17a2b8">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number"><?php echo $totalStats['avg_progress']; ?>%</div>
                            <div class="stat-label">متوسط التقدم</div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-chart-line" style="font-size: 2rem; color: #17a2b8; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Children Overview -->
            <div class="col-lg-8">
                <div class="content-card mb-4">
                    <div class="content-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-child me-2"></i>نظرة عامة على الأبناء
                        </h5>
                    </div>
                    <div class="content-card-body">
                        <?php if (empty($children)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                                <h5>لا يوجد أبناء مسجلين</h5>
                                <p class="text-muted">يرجى التواصل مع الإدارة لربط حسابات الأبناء</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($children as $child): ?>
                                <?php $stats = $childrenStats[$child['id']]; ?>
                                <div class="child-card" style="--grade-color: <?php echo $child['grade_color']; ?>">
                                    <div class="row align-items-center">
                                        <div class="col-auto">
                                            <div class="child-avatar">
                                                <?php echo strtoupper(substr($child['name'], 0, 1)); ?>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <h5 class="mb-1"><?php echo htmlspecialchars($child['name']); ?></h5>
                                            <p class="text-muted mb-2"><?php echo htmlspecialchars($child['grade_name']); ?></p>
                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <small class="text-muted d-block">دروس مكتملة</small>
                                                    <strong class="text-success"><?php echo $stats['completed_lessons']; ?></strong>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted d-block">قيد التقدم</small>
                                                    <strong class="text-warning"><?php echo $stats['in_progress_lessons']; ?></strong>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted d-block">آخر نشاط</small>
                                                    <strong class="text-info"><?php echo formatArabicDate($child['last_login'] ?: $child['created_at']); ?></strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <div class="progress-ring">
                                                <svg>
                                                    <circle class="bg" cx="40" cy="40" r="32"></circle>
                                                    <circle class="progress" cx="40" cy="40" r="32" 
                                                            style="--grade-color: <?php echo $child['grade_color']; ?>; stroke-dasharray: <?php echo (2 * 3.14159 * 32 * $stats['avg_progress'] / 100); ?> <?php echo (2 * 3.14159 * 32); ?>"></circle>
                                                </svg>
                                                <div class="progress-text" style="--grade-color: <?php echo $child['grade_color']; ?>">
                                                    <?php echo round($stats['avg_progress']); ?>%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <a href="child-details.php?id=<?php echo $child['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i>التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="col-lg-4">
                <div class="content-card">
                    <div class="content-card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>آخر الأنشطة
                        </h5>
                    </div>
                    <div class="content-card-body">
                        <?php if (empty($recentActivities)): ?>
                            <p class="text-muted text-center">لا توجد أنشطة حديثة</p>
                        <?php else: ?>
                            <?php foreach ($recentActivities as $activity): ?>
                                <div class="activity-item" style="--activity-color: <?php echo $activity['progress'] >= 100 ? '#28a745' : '#ffc107'; ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="activity-icon">
                                            <i class="fas <?php echo $activity['progress'] >= 100 ? 'fa-check' : 'fa-play'; ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($activity['student_name']); ?></h6>
                                            <p class="mb-1 small"><?php echo htmlspecialchars($activity['lesson_title']); ?></p>
                                            <small class="text-muted">
                                                <?php echo $activity['progress'] >= 100 ? 'أكمل' : 'تقدم في'; ?> - 
                                                <?php echo formatArabicDate($activity['updated_at']); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-<?php echo $activity['progress'] >= 100 ? 'success' : 'warning'; ?>">
                                                <?php echo round($activity['progress']); ?>%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التحريك للبطاقات
            const cards = document.querySelectorAll('.stat-card, .child-card, .content-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
