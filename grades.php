<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// جلب الصفوف المتاحة للمستخدم
if ($user) {
    $grades = getAvailableGrades($user['id']);
} else {
    $grades = getAllGrades();
}

// تجميع الصفوف حسب المرحلة
$gradesByLevel = [
    'primary' => [],
    'middle' => [],
    'secondary' => []
];

foreach ($grades as $grade) {
    $gradesByLevel[$grade['level']][] = $grade;
}

$levelNames = [
    'primary' => 'المرحلة الابتدائية',
    'middle' => 'المرحلة المتوسطة',
    'secondary' => 'المرحلة الثانوية'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفوف الدراسية - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .grade-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        .grade-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--grade-color, #007bff);
        }
        .grade-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: var(--grade-color, #007bff);
        }
        .grade-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
            background: var(--grade-color, #007bff);
        }
        .grade-name {
            color: var(--dark-color);
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        .grade-description {
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }
        .grade-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--grade-color, #007bff);
        }
        .stat-label {
            font-size: 0.8rem;
            color: var(--secondary-color);
        }
        .level-section {
            margin-bottom: 4rem;
        }
        .level-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }
        .level-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--secondary-color);
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold mb-3">الصفوف الدراسية</h1>
                    <p class="lead mb-0">اختر الصف المناسب لك وابدأ رحلتك التعليمية</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <?php if (empty($grades)): ?>
                <div class="empty-state">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>لا توجد صفوف متاحة</h3>
                    <p>لا توجد صفوف دراسية متاحة لك حالياً</p>
                    <?php if (!$user): ?>
                        <a href="register.php" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>إنشاء حساب للوصول للمحتوى
                        </a>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <?php foreach ($levelNames as $level => $levelName): ?>
                    <?php if (!empty($gradesByLevel[$level])): ?>
                        <div class="level-section">
                            <h2 class="level-title"><?php echo $levelName; ?></h2>
                            
                            <div class="row g-4">
                                <?php foreach ($gradesByLevel[$level] as $grade): ?>
                                    <?php
                                    // جلب إحصائيات الصف
                                    $subjectsCount = fetchOne("SELECT COUNT(*) as count FROM subjects WHERE grade_id = :grade_id AND status = 'active'", ['grade_id' => $grade['id']]);
                                    $lessonsCount = fetchOne("SELECT COUNT(*) as count FROM lessons l JOIN subjects s ON l.subject_id = s.id WHERE s.grade_id = :grade_id AND l.status = 'published'", ['grade_id' => $grade['id']]);
                                    $studentsCount = fetchOne("SELECT COUNT(*) as count FROM users WHERE grade_id = :grade_id AND role = 'student' AND status = 'active'", ['grade_id' => $grade['id']]);
                                    ?>
                                    <div class="col-lg-4 col-md-6">
                                        <div class="grade-card" style="--grade-color: <?php echo $grade['color']; ?>">
                                            <div class="grade-icon">
                                                <i class="fas <?php echo $grade['icon']; ?>"></i>
                                            </div>
                                            
                                            <h3 class="grade-name"><?php echo htmlspecialchars($grade['name']); ?></h3>
                                            
                                            <?php if ($grade['description']): ?>
                                                <p class="grade-description"><?php echo htmlspecialchars($grade['description']); ?></p>
                                            <?php endif; ?>
                                            
                                            <div class="grade-stats">
                                                <div class="stat-item">
                                                    <div class="stat-number"><?php echo $subjectsCount['count']; ?></div>
                                                    <div class="stat-label">مادة</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-number"><?php echo $lessonsCount['count']; ?></div>
                                                    <div class="stat-label">درس</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-number"><?php echo $studentsCount['count']; ?></div>
                                                    <div class="stat-label">طالب</div>
                                                </div>
                                            </div>
                                            
                                            <a href="subjects.php?grade=<?php echo $grade['id']; ?>" 
                                               class="btn btn-primary btn-lg w-100">
                                                <i class="fas fa-arrow-left me-2"></i>استكشف المواد
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التحريك
            const gradeCards = document.querySelectorAll('.grade-card');
            
            // مراقب التقاطع للتحريك عند الظهور
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            }, {
                threshold: 0.1
            });
            
            // تطبيق التأثيرات
            gradeCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
            
            // تحسين الروابط
            const gradeLinks = document.querySelectorAll('.grade-card a');
            gradeLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // إضافة تأثير التحميل
                    const button = this;
                    const originalText = button.innerHTML;
                    
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
                    button.disabled = true;
                    
                    // السماح للرابط بالعمل بعد تأخير قصير
                    setTimeout(() => {
                        window.location.href = this.href;
                    }, 500);
                    
                    e.preventDefault();
                });
            });
        });
    </script>
</body>
</html>
