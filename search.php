<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

$query = isset($_GET['q']) ? sanitizeInput($_GET['q']) : '';
$results = [];
$totalResults = 0;

if (!empty($query)) {
    // البحث في الدروس
    $lessonQuery = "
        SELECT l.id, l.title, l.description, l.views_count, l.created_at,
               s.name as subject_name, s.color as subject_color,
               g.name as grade_name, g.id as grade_id
        FROM lessons l 
        JOIN subjects s ON l.subject_id = s.id 
        JOIN grades g ON s.grade_id = g.id 
        WHERE l.status = 'published' 
        AND (l.title LIKE :query OR l.description LIKE :query OR l.keywords LIKE :query)
    ";
    
    // إضافة قيود الوصول للطلاب
    if ($user && $user['role'] == 'student' && $user['grade_id']) {
        $lessonQuery .= " AND g.id = :grade_id";
    }
    
    $lessonQuery .= " ORDER BY l.views_count DESC, l.created_at DESC LIMIT 20";
    
    $params = ['query' => "%$query%"];
    if ($user && $user['role'] == 'student' && $user['grade_id']) {
        $params['grade_id'] = $user['grade_id'];
    }
    
    $lessons = fetchAll($lessonQuery, $params);
    
    foreach ($lessons as $lesson) {
        // التحقق من صلاحية الوصول
        if ($user && !canAccessGrade($user['id'], $lesson['grade_id'])) {
            continue;
        }
        
        $results[] = [
            'type' => 'lesson',
            'data' => $lesson
        ];
    }
    
    // البحث في المواد الدراسية
    $subjectQuery = "
        SELECT s.id, s.name, s.description, s.color, s.icon,
               g.name as grade_name, g.id as grade_id,
               COUNT(l.id) as lessons_count
        FROM subjects s 
        JOIN grades g ON s.grade_id = g.id 
        LEFT JOIN lessons l ON s.id = l.subject_id AND l.status = 'published'
        WHERE s.status = 'active' 
        AND (s.name LIKE :query OR s.description LIKE :query)
    ";
    
    if ($user && $user['role'] == 'student' && $user['grade_id']) {
        $subjectQuery .= " AND g.id = :grade_id";
    }
    
    $subjectQuery .= " GROUP BY s.id ORDER BY s.name ASC LIMIT 10";
    
    $subjects = fetchAll($subjectQuery, $params);
    
    foreach ($subjects as $subject) {
        if ($user && !canAccessGrade($user['id'], $subject['grade_id'])) {
            continue;
        }
        
        $results[] = [
            'type' => 'subject',
            'data' => $subject
        ];
    }
    
    $totalResults = count($results);
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث<?php echo $query ? ' - ' . htmlspecialchars($query) : ''; ?> - منصة التعليم التفاعلي</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .search-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .search-input {
            border: none;
            border-radius: 50px;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .search-btn {
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
        }
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid var(--result-color, #007bff);
        }
        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .result-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .result-type.lesson {
            background: #e3f2fd;
            color: #1976d2;
        }
        .result-type.subject {
            background: #e8f5e8;
            color: #388e3c;
        }
        .result-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.75rem;
        }
        .result-description {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--secondary-color);
        }
        .result-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--result-color, #007bff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-left: 1rem;
        }
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--secondary-color);
        }
        .no-results i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        .search-suggestions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        .popular-searches {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .search-tag {
            display: inline-block;
            background: #e9ecef;
            color: #495057;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            text-decoration: none;
            margin: 0.25rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        .search-tag:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="search-box">
                <h1 class="display-6 fw-bold mb-3 text-center">
                    <i class="fas fa-search me-3"></i>البحث في المنصة
                </h1>
                
                <form method="GET" class="row g-3">
                    <div class="col-md-9">
                        <input type="text" class="form-control search-input" name="q" 
                               placeholder="ابحث عن الدروس والمواد..." 
                               value="<?php echo htmlspecialchars($query); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-light search-btn w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </form>
                
                <?php if ($query): ?>
                    <div class="mt-3 text-center">
                        <p class="mb-0">نتائج البحث عن: <strong>"<?php echo htmlspecialchars($query); ?>"</strong></p>
                        <small>تم العثور على <?php echo $totalResults; ?> نتيجة</small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <div class="row">
                <!-- Search Results -->
                <div class="col-lg-8">
                    <?php if ($query && !empty($results)): ?>
                        <h3 class="mb-4">
                            <i class="fas fa-list me-2"></i>نتائج البحث (<?php echo $totalResults; ?>)
                        </h3>
                        
                        <?php foreach ($results as $result): ?>
                            <?php if ($result['type'] == 'lesson'): ?>
                                <div class="result-card" style="--result-color: <?php echo $result['data']['subject_color']; ?>">
                                    <div class="d-flex">
                                        <div class="result-icon">
                                            <i class="fas fa-play"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <span class="result-type lesson">درس</span>
                                            <h4 class="result-title">
                                                <a href="lesson.php?id=<?php echo $result['data']['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($result['data']['title']); ?>
                                                </a>
                                            </h4>
                                            <p class="result-description">
                                                <?php echo htmlspecialchars(truncateText($result['data']['description'], 150)); ?>
                                            </p>
                                            <div class="result-meta">
                                                <span>
                                                    <i class="fas fa-layer-group me-1"></i>
                                                    <?php echo htmlspecialchars($result['data']['grade_name']); ?> - 
                                                    <?php echo htmlspecialchars($result['data']['subject_name']); ?>
                                                </span>
                                                <span>
                                                    <i class="fas fa-eye me-1"></i>
                                                    <?php echo $result['data']['views_count']; ?> مشاهدة
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php elseif ($result['type'] == 'subject'): ?>
                                <div class="result-card" style="--result-color: <?php echo $result['data']['color']; ?>">
                                    <div class="d-flex">
                                        <div class="result-icon">
                                            <i class="fas <?php echo $result['data']['icon']; ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <span class="result-type subject">مادة دراسية</span>
                                            <h4 class="result-title">
                                                <a href="lessons.php?subject=<?php echo $result['data']['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($result['data']['name']); ?>
                                                </a>
                                            </h4>
                                            <p class="result-description">
                                                <?php echo htmlspecialchars($result['data']['description'] ?: 'مادة دراسية شاملة تحتوي على دروس متنوعة'); ?>
                                            </p>
                                            <div class="result-meta">
                                                <span>
                                                    <i class="fas fa-layer-group me-1"></i>
                                                    <?php echo htmlspecialchars($result['data']['grade_name']); ?>
                                                </span>
                                                <span>
                                                    <i class="fas fa-book me-1"></i>
                                                    <?php echo $result['data']['lessons_count']; ?> درس
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                        
                    <?php elseif ($query): ?>
                        <div class="no-results">
                            <i class="fas fa-search"></i>
                            <h3>لم يتم العثور على نتائج</h3>
                            <p>لم نتمكن من العثور على أي نتائج تطابق بحثك "<strong><?php echo htmlspecialchars($query); ?></strong>"</p>
                            
                            <div class="search-suggestions">
                                <h6><i class="fas fa-lightbulb me-2"></i>اقتراحات للبحث:</h6>
                                <ul class="text-start">
                                    <li>تأكد من كتابة الكلمات بشكل صحيح</li>
                                    <li>جرب كلمات مفتاحية أخرى</li>
                                    <li>استخدم كلمات أقل أو أكثر عمومية</li>
                                    <li>تصفح الصفوف الدراسية مباشرة</li>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Popular Searches -->
                    <div class="popular-searches">
                        <h5 class="mb-3">
                            <i class="fas fa-fire me-2"></i>عمليات بحث شائعة
                        </h5>
                        <div>
                            <a href="search.php?q=الحروف العربية" class="search-tag">الحروف العربية</a>
                            <a href="search.php?q=الرياضيات" class="search-tag">الرياضيات</a>
                            <a href="search.php?q=العلوم" class="search-tag">العلوم</a>
                            <a href="search.php?q=التربية الإسلامية" class="search-tag">التربية الإسلامية</a>
                            <a href="search.php?q=اللغة الإنجليزية" class="search-tag">اللغة الإنجليزية</a>
                            <a href="search.php?q=التاريخ" class="search-tag">التاريخ</a>
                            <a href="search.php?q=الجغرافيا" class="search-tag">الجغرافيا</a>
                            <a href="search.php?q=الفيزياء" class="search-tag">الفيزياء</a>
                            <a href="search.php?q=الكيمياء" class="search-tag">الكيمياء</a>
                            <a href="search.php?q=الأحياء" class="search-tag">الأحياء</a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="popular-searches">
                        <h5 class="mb-3">
                            <i class="fas fa-link me-2"></i>روابط سريعة
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="grades.php" class="btn btn-outline-primary">
                                <i class="fas fa-layer-group me-2"></i>تصفح الصفوف
                            </a>
                            <?php if ($user): ?>
                                <a href="my-courses.php" class="btn btn-outline-success">
                                    <i class="fas fa-book-open me-2"></i>دروسي
                                </a>
                            <?php else: ?>
                                <a href="register.php" class="btn btn-outline-success">
                                    <i class="fas fa-user-plus me-2"></i>إنشاء حساب
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات التحريك للنتائج
            const resultCards = document.querySelectorAll('.result-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            resultCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
            
            // تحسين تجربة البحث
            const searchInput = document.querySelector('input[name="q"]');
            if (searchInput) {
                searchInput.focus();
                
                // إضافة اقتراحات البحث (يمكن تطويرها لاحقاً)
                searchInput.addEventListener('input', function() {
                    // يمكن إضافة AJAX للاقتراحات الفورية هنا
                });
            }
        });
    </script>
</body>
</html>
