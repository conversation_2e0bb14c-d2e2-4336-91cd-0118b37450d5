<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول']);
    exit;
}

$user = getUserById($_SESSION['user_id']);
if (!$user) {
    echo json_encode(['success' => false, 'message' => 'مستخدم غير صحيح']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['lesson_id']) || !isset($input['action'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit;
}

$lessonId = (int)$input['lesson_id'];
$action = $input['action']; // 'add' or 'remove'

// التحقق من صحة البيانات
if ($lessonId <= 0 || !in_array($action, ['add', 'remove'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

// التحقق من وجود الدرس
$lesson = getLessonById($lessonId);
if (!$lesson) {
    echo json_encode(['success' => false, 'message' => 'الدرس غير موجود']);
    exit;
}

// التحقق من صلاحية الوصول للصف
$subjectData = fetchOne("SELECT grade_id FROM subjects WHERE id = :id", ['id' => $lesson['subject_id']]);
if (!canAccessGrade($user['id'], $subjectData['grade_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول لهذا الدرس']);
    exit;
}

try {
    if ($action == 'add') {
        // إضافة للمفضلة
        $existing = fetchOne("SELECT id FROM favorites WHERE user_id = :user_id AND lesson_id = :lesson_id", [
            'user_id' => $user['id'],
            'lesson_id' => $lessonId
        ]);
        
        if ($existing) {
            echo json_encode(['success' => false, 'message' => 'الدرس موجود في المفضلة بالفعل']);
            exit;
        }
        
        $favoriteData = [
            'user_id' => $user['id'],
            'lesson_id' => $lessonId
        ];
        
        $result = insertRecord('favorites', $favoriteData);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الدرس للمفضلة',
                'action' => 'added'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة الدرس للمفضلة']);
        }
        
    } else {
        // إزالة من المفضلة
        $result = deleteRecord('favorites', 'user_id = :user_id AND lesson_id = :lesson_id', [
            'user_id' => $user['id'],
            'lesson_id' => $lessonId
        ]);
        
        if ($result > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'تم إزالة الدرس من المفضلة',
                'action' => 'removed'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'الدرس غير موجود في المفضلة']);
        }
    }
    
} catch (Exception $e) {
    error_log("Favorite toggle error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
