<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من معرف المادة
if (!isset($_GET['subject']) || !is_numeric($_GET['subject'])) {
    header('Location: grades.php');
    exit;
}

$subjectId = (int)$_GET['subject'];

// التحقق من حالة تسجيل الدخول
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// جلب بيانات المادة والصف
$subject = fetchOne("
    SELECT s.*, g.name as grade_name, g.color as grade_color, g.icon as grade_icon 
    FROM subjects s 
    JOIN grades g ON s.grade_id = g.id 
    WHERE s.id = :id
", ['id' => $subjectId]);

if (!$subject) {
    header('Location: grades.php');
    exit;
}

// التحقق من صلاحية الوصول للصف
if ($user && !canAccessGrade($user['id'], $subject['grade_id'])) {
    header('Location: grades.php');
    exit;
}

// جلب الدروس للمادة
$lessons = getLessonsBySubject($subjectId);

// جلب تقدم الطالب إذا كان مسجل دخوله
$userProgress = [];
if ($user && $user['role'] == 'student') {
    foreach ($lessons as $lesson) {
        $progress = getLessonProgress($user['id'], $lesson['id']);
        $userProgress[$lesson['id']] = $progress ? $progress['progress'] : 0;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($subject['name']); ?> - الدروس</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .page-header {
            background: linear-gradient(135deg, <?php echo $subject['color']; ?> 0%, <?php echo $subject['color']; ?>CC 100%);
            color: white;
            padding: 4rem 0 2rem;
            margin-top: 76px;
        }
        .lesson-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            overflow: hidden;
            position: relative;
        }
        .lesson-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: <?php echo $subject['color']; ?>;
        }
        .lesson-thumbnail {
            height: 200px;
            background: linear-gradient(135deg, <?php echo $subject['color']; ?>20 0%, <?php echo $subject['color']; ?>40 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .lesson-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .lesson-thumbnail .play-button {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: <?php echo $subject['color']; ?>;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }
        .lesson-thumbnail .play-button:hover {
            background: white;
            transform: scale(1.1);
        }
        .lesson-content {
            padding: 1.5rem;
        }
        .lesson-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }
        .lesson-description {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        .lesson-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.875rem;
            color: var(--secondary-color);
        }
        .lesson-progress {
            margin-bottom: 1rem;
        }
        .progress {
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
        }
        .progress-bar {
            background: <?php echo $subject['color']; ?>;
            border-radius: 10px;
        }
        .lesson-badges {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        .lesson-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .badge-difficulty-easy { background: #d4edda; color: #155724; }
        .badge-difficulty-medium { background: #fff3cd; color: #856404; }
        .badge-difficulty-hard { background: #f8d7da; color: #721c24; }
        .badge-video { background: #e3f2fd; color: #1976d2; }
        .badge-files { background: #f3e5f5; color: #7b1fa2; }
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--secondary-color);
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        .subject-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .subject-icon-large {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1.5rem;
            margin-left: 1rem;
        }
        .lesson-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: <?php echo $subject['color']; ?>;
        }
        .stat-label {
            font-size: 0.8rem;
            color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-graduation-cap me-2"></i>
                منصة التعليم التفاعلي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="grades.php">
                            <i class="fas fa-layer-group me-1"></i>الصفوف الدراسية
                        </a>
                    </li>
                    <?php if ($user): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="my-courses.php">
                                <i class="fas fa-book-open me-1"></i>دروسي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($user): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($user['name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                                </a></li>
                                <?php if ($user['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                    </a></li>
                                <?php elseif ($user['role'] == 'parent'): ?>
                                    <li><a class="dropdown-item" href="parent/dashboard.php">
                                        <i class="fas fa-child me-2"></i>متابعة الأبناء
                                    </a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php" class="text-white-50">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="grades.php" class="text-white-50">الصفوف الدراسية</a></li>
                    <li class="breadcrumb-item"><a href="subjects.php?grade=<?php echo $subject['grade_id']; ?>" class="text-white-50"><?php echo htmlspecialchars($subject['grade_name']); ?></a></li>
                    <li class="breadcrumb-item active text-white"><?php echo htmlspecialchars($subject['name']); ?></li>
                </ol>
            </nav>
            
            <div class="subject-info">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="subject-icon-large">
                            <i class="fas <?php echo $subject['icon']; ?>"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h1 class="display-5 fw-bold mb-2"><?php echo htmlspecialchars($subject['name']); ?></h1>
                        <p class="lead mb-0"><?php echo htmlspecialchars($subject['grade_name']); ?></p>
                        <?php if ($subject['description']): ?>
                            <p class="mb-0 mt-2"><?php echo htmlspecialchars($subject['description']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <?php if (!empty($lessons)): ?>
                <!-- Statistics -->
                <div class="lesson-stats">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo count($lessons); ?></div>
                                <div class="stat-label">إجمالي الدروس</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo count(array_filter($lessons, function($l) { return !empty($l['video_url']); })); ?></div>
                                <div class="stat-label">دروس بفيديو</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <div class="stat-number"><?php echo array_sum(array_column($lessons, 'estimated_time')) ?: 0; ?></div>
                                <div class="stat-label">دقيقة إجمالي</div>
                            </div>
                        </div>
                        <?php if ($user && $user['role'] == 'student'): ?>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo count(array_filter($userProgress, function($p) { return $p >= 100; })); ?></div>
                                    <div class="stat-label">دروس مكتملة</div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Lessons Grid -->
                <div class="row g-4">
                    <?php foreach ($lessons as $lesson): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="lesson-card">
                                <div class="lesson-thumbnail">
                                    <?php if ($lesson['thumbnail']): ?>
                                        <img src="uploads/thumbnails/<?php echo htmlspecialchars($lesson['thumbnail']); ?>" alt="<?php echo htmlspecialchars($lesson['title']); ?>">
                                    <?php else: ?>
                                        <i class="fas fa-chalkboard-teacher" style="font-size: 3rem; color: <?php echo $subject['color']; ?>; opacity: 0.3;"></i>
                                    <?php endif; ?>
                                    
                                    <?php if ($lesson['video_url']): ?>
                                        <div class="play-button">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="lesson-content">
                                    <h3 class="lesson-title"><?php echo htmlspecialchars($lesson['title']); ?></h3>
                                    
                                    <?php if ($lesson['description']): ?>
                                        <p class="lesson-description"><?php echo htmlspecialchars(substr($lesson['description'], 0, 120)) . (strlen($lesson['description']) > 120 ? '...' : ''); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="lesson-meta">
                                        <span><i class="fas fa-clock me-1"></i><?php echo $lesson['estimated_time'] ? $lesson['estimated_time'] . ' دقيقة' : 'غير محدد'; ?></span>
                                        <span><i class="fas fa-eye me-1"></i><?php echo $lesson['views_count']; ?> مشاهدة</span>
                                    </div>
                                    
                                    <div class="lesson-badges">
                                        <span class="lesson-badge badge-difficulty-<?php echo $lesson['difficulty']; ?>">
                                            <?php 
                                            $difficulties = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
                                            echo $difficulties[$lesson['difficulty']];
                                            ?>
                                        </span>
                                        <?php if ($lesson['video_url']): ?>
                                            <span class="lesson-badge badge-video">
                                                <i class="fas fa-play me-1"></i>فيديو
                                            </span>
                                        <?php endif; ?>
                                        <?php 
                                        $filesCount = fetchOne("SELECT COUNT(*) as count FROM lesson_files WHERE lesson_id = :lesson_id", ['lesson_id' => $lesson['id']]);
                                        if ($filesCount['count'] > 0):
                                        ?>
                                            <span class="lesson-badge badge-files">
                                                <i class="fas fa-file me-1"></i><?php echo $filesCount['count']; ?> ملف
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ($user && $user['role'] == 'student' && isset($userProgress[$lesson['id']])): ?>
                                        <div class="lesson-progress">
                                            <div class="d-flex justify-content-between mb-1">
                                                <small>التقدم</small>
                                                <small><?php echo round($userProgress[$lesson['id']]); ?>%</small>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar" style="width: <?php echo $userProgress[$lesson['id']]; ?>%"></div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <a href="lesson.php?id=<?php echo $lesson['id']; ?>" class="btn btn-primary w-100">
                                        <i class="fas fa-arrow-left me-2"></i>بدء الدرس
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h3>لا توجد دروس متاحة</h3>
                    <p>لا توجد دروس متاحة لهذه المادة حالياً</p>
                    <a href="subjects.php?grade=<?php echo $subject['grade_id']; ?>" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمواد
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-graduation-cap me-2"></i>منصة التعليم التفاعلي</h5>
                    <p class="mb-0">منصة تعليمية شاملة لجميع المراحل الدراسية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2024 جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات التحريك للبطاقات
            const lessonCards = document.querySelectorAll('.lesson-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                });
            });
            
            lessonCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
